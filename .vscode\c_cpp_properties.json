{"configurations": [{"name": "kyl<PERSON>-<PERSON>t5.15.2", "includePath": ["${workspaceFolder}/**", "/opt/Qt/5.15.2/gcc_64/include/**", "${userHome}/4in1_libs/**"], "defines": [], "compilerPath": "/usr/bin/g++", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64"}, {"name": "Win32-MSVC2019-Qt5.15.2-32bit", "includePath": ["c:/4in1_libs/**", "${workspaceFolder}/**", "C:/Qt/5.15.2/msvc2019/include/**", "C:/Qt/5.15.2/msvc2019/include/QtCore", "C:/Qt/5.15.2/msvc2019/include/QtGui", "C:/Qt/5.15.2/msvc2019/include/QtWidgets", "C:/Qt/5.15.2/msvc2019/include/QtNetwork", "C:/Qt/5.15.2/msvc2019/include/QtSql", "C:/Qt/5.15.2/msvc2019/include/QtXml", "C:/Qt/5.15.2/msvc2019/include/QtConcurrent", "C:/Qt/5.15.2/msvc2019/include/QtPrintSupport"], "defines": ["UNICODE", "_UNICODE", "WIN32", "_WIN32", "QT_DLL"], "compilerPath": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx86\\x86\\cl.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x86"}], "version": 4}