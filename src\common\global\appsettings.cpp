

// appsettings.cpp
// 实现 AppSettings 类，负责应用全局配置的读取、写入与信号通知

#include "appsettings.h" // 头文件声明
// #include "qjsonrpc_with_ws_client.h"
// #include "wsrpcclient.h"
#include "client/wsrpcclient.h" // WebSocket RPC 客户端

#ifndef Q_OS_WASM
#define CONFIG_FILE_PATH (QCoreApplication::applicationDirPath() + "/configs.ini")
#define LANGUAGE_CONFIG_PATH (QCoreApplication::applicationDirPath() + "/languages.config.json")
#endif

// 构造函数，初始化配置并连接服务器
AppSettings::AppSettings(QObject* parent)
    : QObject(parent)
{
    loadLanguageConfig(LANGUAGE_CONFIG_PATH);
    readConfigs();
    WSRPCClient::instance().connectToServer(serverHost());
}

// 析构函数，释放配置对象
AppSettings::~AppSettings()
{
    if (m_settings) {
        delete m_settings;
        m_settings = nullptr;
    }
}
// 读取languages.config.json到m_languageMap
bool AppSettings::loadLanguageConfig(const QString& jsonPath)
{
    QFile file(jsonPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open language config file:" << jsonPath;
        return false;
    }
    QByteArray data = file.readAll();
    file.close();
    QJsonParseError err;
    QJsonDocument doc = QJsonDocument::fromJson(data, &err);
    if (err.error != QJsonParseError::NoError || !doc.isObject()) {
        qWarning() << "Failed to parse language config JSON:" << err.errorString();
        return false;
    }
    QJsonObject root = doc.object();
    m_languageMap.clear();
    for (auto it = root.begin(); it != root.end(); ++it) {
        QString key = it.key();
        QJsonObject obj = it.value().toObject();
        bool enable = obj.value("enable").toBool();
        QString desc = obj.value("desc").toString();
        QString alias = obj.value("alias").toString();

        if (!enable || desc.isEmpty() || alias.isEmpty()) {
            qDebug() << "Skipping invalid or disabled language config:" << key;
            continue; // 跳过无效或禁用的语言配置
        }
        m_languageMap.insert(key, LanguageConfig(key, desc, alias));
    }
    return true;
}
// 获取 AppSettings 单例对象
AppSettings& AppSettings::instance()
{
    static AppSettings in;
    return in;
}

// 读取配置文件，支持桌面和 WebAssembly 平台
void AppSettings::readConfigs()
{
    if (!m_settings) {
#ifdef Q_OS_WASM
        m_settings = new QSettings("4In1AppSettings", "Configs");
#else
        m_settings = new QSettings(CONFIG_FILE_PATH, QSettings::IniFormat);
#endif
    }
    // Lambda：读取配置项到成员变量
    auto settingsReady = [=]() {
        m_serverHost = m_settings->value("ServerHost", "").toString();
        m_currentLanguageKey = m_settings->value("Language", "").toString();
        m_recentProjects = m_settings->value("Recents", {}).toList();
        m_isDebugMode = m_settings->value("DebugMode", "0").toBool();
    };
#ifdef Q_OS_WASM
    // WASM下异步等待配置文件可用
    std::function<void(void)>* testSettingsReady = new std::function<void(void)>();
    *testSettingsReady = [=]() {
        if (m_settings->status() == QSettings::NoError) {
            delete testSettingsReady;
            settingsReady();
        } else {
            QTimer::singleShot(10, *testSettingsReady);
        }
    };
    (*testSettingsReady)();
#else
    m_settings->beginGroup("Configs");
    settingsReady();
    m_settings->endGroup();
#endif
}

// 写入配置文件，支持桌面和 WebAssembly 平台
void AppSettings::writeConfigs()
{
    // Lambda：写成员变量到配置项
    auto settingsWrite = [=]() {
        m_settings->setValue("ServerHost", m_serverHost);
        m_settings->setValue("Language", m_currentLanguageKey);
        m_settings->setValue("Recents", m_recentProjects);
        m_settings->setValue("DebugMode", m_isDebugMode);
    };
    if (m_settings) {
#ifdef Q_OS_WASM
        settingsWrite();
#else
        m_settings->beginGroup("Configs");
        settingsWrite();
        m_settings->endGroup();
#endif
    }
}

bool AppSettings::isDebugMode() const
{
    return m_isDebugMode;
}

void AppSettings::setIsDebugMode(bool newIsDebugMode)
{
    m_isDebugMode = newIsDebugMode;
}

AppSettings::LanguageConfig AppSettings::currentLanguageInfo() const
{
    if (!m_currentLanguageKey.isEmpty() && m_languageMap.contains(m_currentLanguageKey)) {
        return m_languageMap.value(m_currentLanguageKey);
    }
    if (m_languageMap.contains("en_US")) {
        return m_languageMap.value("en_US");
    }
    return LanguageConfig();
}

QList<AppSettings::LanguageConfig> AppSettings::allLanguages() const
{
    return m_languageMap.values();
}

QVariantList AppSettings::recentProjects() const
{
    return m_recentProjects;
}

void AppSettings::setRecentProjects(const QVariantList& newRecentProjects)
{
    m_recentProjects = newRecentProjects;
    writeConfigs();
}

void AppSettings::appendRecentProject(const QVariant& projectid)
{
    m_recentProjects.removeAll(projectid);
    m_recentProjects.insert(0, projectid);
    writeConfigs();
}

bool AppSettings::isUseChinese() const
{
    return m_currentLanguageKey == "zh_CN" || m_currentLanguageKey == "zh_cn";
}

// void AppSettings::setIsUseChinese(bool newIsUseChinese)
// {
//     if (newIsUseChinese != m_isUseChinese) {
//         m_isUseChinese = newIsUseChinese;
//         writeConfigs();
//         emit isUseChineseChanged();
//     }
// }

QString AppSettings::currentLanguageKey() const
{
    return m_currentLanguageKey;
}

void AppSettings::setCurrentLanguageKey(const QString& key)
{
    m_currentLanguageKey = key;
    writeConfigs();
}

QString AppSettings::currentLanguageStandrandKey() const
{
    return currentLanguageInfo().alias;
}

QString AppSettings::serverHost() const
{
    return m_serverHost;
}

void AppSettings::setServerHost(const QString& newServerHost)
{
    if (newServerHost != m_serverHost) {
        m_serverHost = newServerHost;
        WSRPCClient::instance().reconnect(newServerHost);
        writeConfigs();
        emit serverHostChanged();
    }
}
