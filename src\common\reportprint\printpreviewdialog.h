
#ifndef PRINTPREVIEWDIALOG_H
#define PRINTPREVIEWDIALOG_H

#include <QPrintPreviewDialog>
#ifdef SUPPORT_PDF
class QPdfDocument;
#endif
class QAbstractItemModel;
class PrintPreviewDialogPrivate;

/**
 * @brief 打印预览对话框类
 *
 * 封装QPrintPreviewDialog，支持模型、文件、PDF等多种内容的打印预览。
 * 支持与PrintHelper等工具类协作，便于统一打印体验。
 *
 * 主要功能：
 * - 静态接口支持直接打印文件/模型
 * - 支持自定义绘制槽函数
 * - 支持PDF扩展
 * - 采用PIMPL隐藏实现细节
 */
class PrintPreviewDialog : public QPrintPreviewDialog
{
    Q_OBJECT
    friend class PrintHelper;
    friend class QSharedPointer<PrintPreviewDialog>;
    friend struct QtSharedPointer::ExternalRefCountWithContiguousData<PrintPreviewDialog>;
public:
    /**
     * @brief 静态接口：打印文件
     * @param fileName 文件路径
     */
    static void printFile(const QString &fileName);
    /**
     * @brief 静态接口：打印模型
     * @param model 数据模型
     */
    static void printModel(QAbstractItemModel *model);

public Q_SLOTS:
    /**
     * @brief 打印预览绘制槽
     * @param printer 打印机对象
     */
    void onPaintRequested(QPrinter *printer);
private:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit PrintPreviewDialog(QWidget *parent = nullptr);
    ~PrintPreviewDialog() = default;
    QSharedPointer<PrintPreviewDialogPrivate> d_ptr; ///< PIMPL指针
};

#endif // PRINTPREVIEWDIALOG_H
