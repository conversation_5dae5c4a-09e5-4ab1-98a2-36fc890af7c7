
/**
 * @file userlogindialog.h
 * @brief 用户登录对话框声明文件
 *
 * 提供用户登录界面，支持登录验证、失败计数、键盘事件处理等。
 * 适用于多平台桌面应用，界面基于Qt Designer生成。
 */

#ifndef UserLoginDialog_H
#define UserLoginDialog_H

#include <QWidget>

namespace Ui {
class UserLoginDialog;
}

class UIGlobalState;

/**
 * @brief 用户登录对话框类
 *
 * 负责用户登录界面交互、登录验证、失败次数统计等。
 * 支持回车键登录、ESC关闭等快捷操作。
 */
class UserLoginDialog : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param gl 全局状态指针
     * @param appname 应用名称
     * @param parent 父窗口指针
     */
    explicit UserLoginDialog(UIGlobalState* gl, const QString& appname, QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~UserLoginDialog();

protected:
    /**
     * @brief 事件过滤，处理特殊事件（如回车、ESC等）
     * @param event 事件指针
     * @return 是否处理
     */
    bool event(QEvent* event) override;

    /**
     * @brief 键盘按下事件，支持快捷键登录/关闭
     * @param event 键盘事件指针
     */
    void keyPressEvent(QKeyEvent* event) override;

private slots:
    /**
     * @brief 登录按钮点击槽函数，处理登录逻辑
     */
    void on_pushButtonLogin_clicked();

private:
    Ui::UserLoginDialog* ui;        ///< Qt Designer生成的UI指针
    UIGlobalState* global = nullptr;///< 全局状态指针
    QString m_appname;              ///< 应用名称
    int m_failedCount = 0;          ///< 登录失败次数统计
};

#endif // UserLoginDialog_H
