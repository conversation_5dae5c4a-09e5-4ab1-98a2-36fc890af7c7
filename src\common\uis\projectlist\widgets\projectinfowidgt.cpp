#include "projectinfowidgt.h"
// #include "projectespage.h"
#include "appsettings.h"
#include "bussiness_interfaces_declare_metatypes.h"
#include "global_structs.pb.h"
#include "messagebox.h"
#include "ui_projectinfowidgt.h"
#include "uiglobalstate.h"
#include <QMessageBox>

ProjectInfoWidgt::ProjectInfoWidgt(UIGlobalState* gl, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ProjectInfoWidgt)
    , global(gl)
{
    ui->setupUi(this);
    // ui->createDate->setStyleSheet("border: 0px;");
    // ui->openedDate->setStyleSheet("border: 0px;");
    // ui->spinBox->setStyleSheet("border: 0px;");
    // ui->toolButton->setStyleSheet("background-color: #ffffff;");
    hideInfoWidget();
    connect(ui->open, &QPushButton::pressed, this, &ProjectInfoWidgt::openProject);
    setStationNum(0);
}

ProjectInfoWidgt::~ProjectInfoWidgt()
{
    delete ui;
}

void ProjectInfoWidgt::setProjectName(const QString& name)
{
    ui->projecteName->setText(name);
}

void ProjectInfoWidgt::setCreateTime(const QString& time)
{
    QString _time = time;
    ui->labelCreateDate->setText(_time.replace("T", " "));
}

void ProjectInfoWidgt::setProjectDesc(const QString& desc)
{
    this->setToolTip(desc);
}

void ProjectInfoWidgt::setProjectPath(const QString& path)
{
    ui->projectePath->setText(path);
}

void ProjectInfoWidgt::setOpenedTime(const QString& time)
{
    QString _time = time;
    ui->labelRecentOpen->setText(_time.replace("T", " "));
}

void ProjectInfoWidgt::setStationNum(int num)
{
    ui->labelStationNumber->setText(QString::number(num));
}

void ProjectInfoWidgt::setProjectID(int projectid)
{
    ui->labelProjectID->setText(QString::number(projectid));
}

void ProjectInfoWidgt::showInfoWidget()
{
    ui->mainWidget->show();
}

void ProjectInfoWidgt::hideInfoWidget()
{
    ui->mainWidget->hide();
}

void ProjectInfoWidgt::setIndex(const QModelIndex& index)
{
    m_index = index;
}

void ProjectInfoWidgt::openProject()
{
    Q_ASSERT(global);
    const ::GlobalStructs::ProjectInfo dataValue = m_index.data(Qt::UserRole).value<::GlobalStructs::ProjectInfo>();
    if (global->currentProject() != QString::fromStdString(dataValue.name())) {
        if (messageBoxEx(tr("Are you sure to open the project?")) == QMessageBox::Yes) {
            if (global)
                global->setCurrentProject(dataValue.projectid(), QString::fromStdString(dataValue.name()));
            // emit dynamic_cast<ProjectesPage*>(parent())->openProject(m_index);
            AppSettings::instance().appendRecentProject(dataValue.projectid());
        }
    } else {
        global->setCurrentProject(dataValue.projectid(), QString::fromStdString(dataValue.name()));
    }
}

bool ProjectInfoWidgt::event(QEvent* event)
{
    if (event->type() == QEvent::LanguageChange) {
        // QString name = ui->projecteName->text();
        // QString path = ui->projectePath->text();
        ui->retranslateUi(this);
        // ui->projecteName->setText(name);
        // ui->projectePath->setText(path);
    }

    return QWidget::event(event);
}
