#ifndef _checkresponse_h_
#define _checkresponse_h_

#include "uiglobalstate.h"
#include "wsrpc_resp_funcs.h"

// 检测RPC返回，如果有异常显示错误信息Tooltip
#define CHECK_RPC_ERROR(global, code, msg)                                                                   \
    if (code != WSRPC::ErrorCode::Success) {                                                                 \
        if (global) {                                                                                        \
            emit global->showTooltip(UIGlobalState::TooltipLevel::Error, WSRPC::errorCodeString(code, msg)); \
        }                                                                                                    \
        return;                                                                                              \
    }

// 检测接口返回值返回，如果有异常显示错误信息Tooltip
#define CHECK_RESPONSE(global, resp)                                                                              \
    if (resp.code() != 0) {                                                                                       \
        if (global) {                                                                                             \
            emit global->showTooltip(UIGlobalState::TooltipLevel::Error, QString::fromStdString(resp.message())); \
        }                                                                                                         \
        return;                                                                                                   \
    }

#define CHECK_RESPONSE_JSON(global, suc, msg) CHECK_RPC_ERROR(global, suc, msg)
#endif
