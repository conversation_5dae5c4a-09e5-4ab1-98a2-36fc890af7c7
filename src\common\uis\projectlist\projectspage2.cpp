
/**
 * @file projectspage2.cpp
 * @brief 项目管理主页面（新版）实现文件
 *
 * 实现项目列表、最近打开、项目信息展示、刷新、双击进入等功能，支持多平台Qt桌面应用。
 * 支持项目筛选、数据模型、全局状态、信号槽等，便于团队协作和维护。
 */

#include "projectspage2.h"
#include "appsettings.h"
#include "bussiness_interfaces_caller.h"
#include "checkresponse.h"
#include "messagebox.h"
#include "projectinfowidgt.h"
#include "projectitemdelegate.h"
#include "ui_projectspage2.h"

/**
 * @brief 构造函数
 * @param gl 全局状态指针
 * @param parent 父窗口指针
 */
ProjectsPage2::ProjectsPage2(UIGlobalState* gl, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ProjectsPage2)
    , m_global(gl)
    , m_model(new QStandardItemModel)
{
    ui->setupUi(this);

    // 初始化项目列表视图
    ui->listView->setModel(m_model);
    ui->listView->setItemDelegate(new ProjectItemDelegate());
    ui->listView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->listView->viewport()->setMouseTracking(true);

    // 主分割布局
    this->layout()->addWidget(&m_splitter);
    this->layout()->setContentsMargins(0, 0, 0, 0);
    m_splitter.setHandleWidth(1);
    m_splitter.addWidget(ui->widgetLeft);
    m_splitter.addWidget(ui->widgetCenter);
    m_splitter.addWidget(ui->widgetRight);

    // 右侧项目信息面板
    QVBoxLayout* rightLayout = new QVBoxLayout(ui->widgetRight);
    m_projectInfo = new ProjectInfoWidgt(m_global);
    m_projectInfo->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    rightLayout->addWidget(m_projectInfo);

    // 默认点击“全部项目”按钮，初始化列表
    ui->toolButtonProjectList->click();
    connect(ui->listView->selectionModel(), &QItemSelectionModel::currentChanged,
        this, &ProjectsPage2::selectProjectChanged);

    // ui->pushButtonRefresh->hide();
    // refreshProjectList(false);
}

/**
 * @brief 析构函数
 */
ProjectsPage2::~ProjectsPage2()
{
    delete ui;
}

/**
 * @brief 事件过滤，处理多语言切换时界面文本刷新
 * @param event 事件指针
 * @return 是否处理
 */
bool ProjectsPage2::event(QEvent* event)
{
    if (event->type() == QEvent::LanguageChange) {
        ui->retranslateUi(this);
        if (ui->toolButtonProjectList->isChecked()) {
            ui->labelCurrentListTitle->setText(tr("Project List"));
        } else if (ui->toolButtonRecentlyOpen->isChecked()) {
            ui->labelCurrentListTitle->setText(tr("Recently Opened"));
        } else {
            ui->labelCurrentListTitle->clear();
        }
    }

    return QWidget::event(event);
}

/**
 * @brief 刷新当前项目信息面板
 */
void ProjectsPage2::refreshCurrentProjectInfo()
{
    ui->pushButtonReturn->setEnabled(!m_global->currentProject().isEmpty());
    if (m_global->currentProject().isEmpty()) {
        ui->labelCurrentProjectName->setText("-");
    } else {
        ui->labelCurrentProjectName->setText(m_global->currentProject());
    }
}

/**
 * @brief “全部项目”按钮点击槽，显示全部项目
 */
void ProjectsPage2::on_toolButtonProjectList_clicked()
{
    filterProjects(false);
    ui->labelCurrentListTitle->setText(tr("Project list"));
}

/**
 * @brief “最近打开”按钮点击槽，显示最近打开项目
 */
void ProjectsPage2::on_toolButtonRecentlyOpen_clicked()
{
    filterProjects(true);
    ui->labelCurrentListTitle->setText(tr("Recently opened"));
}

/**
 * @brief “刷新”按钮点击槽，刷新项目列表
 */
void ProjectsPage2::on_pushButtonRefresh_clicked()
{
    Q_ASSERT(m_global);
    refreshProjectList(true);
}

/**
 * @brief 向项目列表添加一项
 * @param data 项目信息结构体
 */
void ProjectsPage2::addProjectToList(const GlobalStructs::ProjectInfo& data)
{
    Q_ASSERT(m_model);
    QStandardItem* itemWidget = new QStandardItem();
    QVariant variant = QVariant::fromValue(data);
    itemWidget->setData(variant, Qt::UserRole);
    m_model->appendRow(itemWidget);
}

/**
 * @brief 界面显示事件，自动刷新项目列表和当前项目信息
 * @param event 显示事件
 */
void ProjectsPage2::showEvent(QShowEvent* event)
{
    refreshProjectList(false);
    refreshCurrentProjectInfo();
    QWidget::showEvent(event);
}

/**
 * @brief 筛选项目（全部/最近打开）并刷新列表
 * @param isRecentOpen 是否仅显示最近打开
 */
void ProjectsPage2::filterProjects(bool isRecentOpen)
{
    Q_ASSERT(m_model);
    Q_ASSERT(m_projectInfo);
    this->setEnabled(false);
    m_model->clear();
    m_projectInfo->hideInfoWidget();
    if (isRecentOpen) {
        Q_FOREACH (QVariant pid, AppSettings::instance().recentProjects()) {
            if (m_projectInfos.contains(pid.toInt())) {
                addProjectToList(m_projectInfos[pid.toInt()]);
            }
        }
    } else {
        Q_FOREACH (GlobalStructs::ProjectInfo projectinfo, m_projectInfos.values()) {
            addProjectToList(projectinfo);
        }
    }
    this->setEnabled(true);
}

/**
 * @brief 项目列表双击槽，进入或切换项目
 * @param index 被双击的索引
 */
void ProjectsPage2::on_listView_doubleClicked(const QModelIndex& index)
{
    Q_ASSERT(m_global);
    const GlobalStructs::ProjectInfo dataValue = index.data(Qt::UserRole).value<GlobalStructs::ProjectInfo>();

    if (!m_global)
        return;

    auto callback = [=](WSRPC::ErrorCode code, const QString& errorString, const GlobalStructs::PermissionResponse& returnValue) {
        CHECK_RPC_ERROR(m_global, code, errorString);
        const QString msg = QObject::tr("User permission verification failed.");
        if (returnValue.respcode().code() != 0) {
            messageBoxEx(msg, true);
            return;
        }

        if (m_global->currentProjectID() != dataValue.projectid()) {
            if (messageBoxEx(QObject::tr("Are you sure to open the project?")) == QMessageBox::Yes) {
                m_global->setCurrentProject(dataValue.projectid(), QString::fromStdString(dataValue.name()));
                AppSettings::instance().appendRecentProject(dataValue.projectid());
                // emit openProject(index);
            }
        } else {
            // messageBoxEx(QObject::tr("The project '%1' has been opened.").arg(dataValue.name().c_str()), true);
            // on_pushButtonReturn_clicked(false);
            m_global->setCurrentProject(dataValue.projectid(), QString::fromStdString(dataValue.name()));
        }
    };
    GlobalStructs::PermissionRequest request;
    if ("rest" == m_global->clientName()) {
        request.set_clientpermission(GlobalStructs::Enum_ClientPermission::CP_REST_READWRITE);
    } else if ("fedis" == m_global->clientName()) {
        request.set_clientpermission(GlobalStructs::Enum_ClientPermission::CP_FEDIS_READ);
    }
    // 检测权限
    request.set_token(m_global->currentUserToken().toStdString());
    request.set_projectid(dataValue.projectid());

    Global::call_getPermission(request, callback, nullptr, DEFAULT_TIMEOUT_INTERVAL,
        AppSettings::instance().currentLanguageStandrandKey(), m_global->clientName());
}

/**
 * @brief “返回”按钮点击槽，切回当前项目
 * @param checked 按钮状态
 */
void ProjectsPage2::on_pushButtonReturn_clicked(bool checked)
{
    m_global->setCurrentProject(m_global->currentProjectID(), m_global->currentProject());
}

/**
 * @brief 刷新项目列表（从后端获取最新数据）
 * @param notice 是否弹出提示
 */
void ProjectsPage2::refreshProjectList(bool notice)
{
    // setEnabled(false);
    ::GlobalStructs::ProjectListRequest req;
    req.set_token(m_global->currentUserToken().toStdString());
    auto callback_func = [=](WSRPC::ErrorCode code, const QString& errorstring, const ::GlobalStructs::ProjectListResponse& ret) {
        Q_ASSERT(m_global);
        m_model->clear();
        m_projectInfos.clear();
        setEnabled(true);
        CHECK_RPC_ERROR(m_global, code, errorstring);
        CHECK_RESPONSE(m_global, ret.respcode());
        for (GlobalStructs::ProjectInfo project : ret.projectinfolist()) {
            m_projectInfos[project.projectid()] = project;
        }
        filterProjects(ui->toolButtonRecentlyOpen->isChecked());
        if (notice)
            emit m_global->showTooltip(UIGlobalState::TooltipLevel::Normal, tr("Project list refresed!"));
    };
    Global::call_getProjectList(req, callback_func, nullptr, DEFAULT_TIMEOUT_INTERVAL, AppSettings::instance().currentLanguageInfo().alias, m_global->clientName());
}

/**
 * @brief 项目选择变化槽，刷新右侧项目信息面板
 * @param current 当前选中索引
 * @param 上一个索引（未用）
 */
void ProjectsPage2::selectProjectChanged(const QModelIndex& current, const QModelIndex&)
{
    if (current.isValid()) {
        const GlobalStructs::ProjectInfo dataValue = current.data(Qt::UserRole).value<GlobalStructs::ProjectInfo>();
        if (m_projectInfo) {
            m_projectInfo->setIndex(current);
            m_projectInfo->setProjectName(QString::fromStdString(dataValue.name()));
            m_projectInfo->setCreateTime(QString::fromStdString(dataValue.createdatetime()));
            m_projectInfo->setOpenedTime(QString::fromStdString(dataValue.recentopen()));
            m_projectInfo->setProjectPath(QString::fromStdString(dataValue.path()));
            m_projectInfo->setProjectDesc(QString::fromStdString(dataValue.desc()));
            m_projectInfo->setProjectID(dataValue.projectid());
            m_projectInfo->setStationNum(dataValue.stationcount());
            m_projectInfo->showInfoWidget();
        }
    }
}

void ProjectsPage2::on_pushButtonLogout_clicked(bool checked)
{
    if (QMessageBox::Yes == messageBoxEx(QObject::tr("Are you sure you want to log out and return to the login screen?"))) {
        GlobalStructs::LogOutRequest req;
        req.set_token(m_global->currentUserToken().toStdString());
        Global::call_logout(req, nullptr, nullptr, DEFAULT_TIMEOUT_INTERVAL, AppSettings::instance().currentLanguageInfo().alias, m_global->clientName());
        QThread::msleep(500);
        m_global->setLoginState(false);
    }
}
