
#ifndef PRINTAPI_H
#define PRINTAPI_H

#include <QMutex>
#include <QObject>
#include <QStringList>
#include <QPageSize>
#include <QPrinter>
#include <QtPrintSupport/QPrintPreviewDialog>

/**
 * @brief 打印API单例类
 *
 * 提供A4报表打印、打印预览等功能，支持多线程安全、页面设置、横纵向、内容自定义等。
 * 适用于Qt桌面应用的报表、表格、内容批量打印。
 *
 * 主要功能：
 * - 单例模式，线程安全
 * - 支持A4纸张、横纵向、列宽、内容自定义
 * - 打印预览对话框集成
 * - 支持多种内容格式扩展
 */
class PrintAPI : public QObject {
    Q_OBJECT
private:
    /**
     * @brief 构造函数（私有，单例模式）
     * @param parent 父对象
     */
    explicit PrintAPI(QObject* parent = 0);
    static PrintAPI* _instance; ///< 单例指针
    QStringList html;           ///< 打印内容HTML片段
    QSharedPointer<QPrintPreviewDialog> printDialog; ///< 打印预览对话框

public:
    /**
     * @brief 获取单例实例
     * @return PrintAPI* 单例指针
     * @note 线程安全，首次调用自动创建
     */
    static PrintAPI* Instance()
    {
        static QMutex mutex;
        if (!_instance) {
            QMutexLocker locker(&mutex);
            if (!_instance) {
                _instance = new PrintAPI;
            }
        }
        return _instance;
    }

    /**
     * @brief 打印A4报表
     * @param title      报表主标题
     * @param subTitle   报表副标题
     * @param columnNames 列名列表
     * @param columnWidths 列宽列表
     * @param content    内容（每行一个字符串）
     * @param landscape  是否横向打印
     * @param check      是否校验内容
     * @param pageSize   页面尺寸，默认A4
     */
    void PrintA4(const QString& title, const QString& subTitle,
        const QList<QString>& columnNames, const QList<int>& columnWidths,
        const QStringList& content, bool landscape, bool check,
        QPageSize pageSize = QPageSize(QPageSize::A4));

    // 扩展接口示例：支持多副标题、多内容区块
    // void PrintA4(const QString& title, const QList<QString>& columnNames, const QList<int>& columnWidths,
    //     const QStringList& subTitle1, const QStringList& subTitle2,
    //     const QStringList& content, bool landscape, bool check,
    //     QPageSize pageSize = QPageSize(QPageSize::A4));

signals:
    // 可扩展：打印完成、错误等信号

private slots:
    /**
     * @brief 打印视图槽函数
     * @param printer 打印机对象
     */
    void printView(QPrinter* printer);
};

#endif // PRINTAPI_H
