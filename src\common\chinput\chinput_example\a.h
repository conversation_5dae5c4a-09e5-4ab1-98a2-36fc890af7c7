#ifndef A_H
#define A_H

#include "chineseinput.h"
#include <QtCore>
#include <QtWidgets>

class A : public QObject {
public:
    A(QObject* parent = nullptr)
    {
        qApp->installEventFilter(this);
    }

protected:
    bool eventFilter(QObject* watched, QEvent* event)
    {

        QLineEdit* l = dynamic_cast<QLineEdit*>(watched);
        if (l && event->type() == QEvent::MouseButtonPress) {
            qDebug() << watched->objectName() << " " << event->type();
            l->setText(ChineseInput().getUserInput(l->text()));
        }

        return QObject::eventFilter(watched, event);
    }
};
#endif // A_H
