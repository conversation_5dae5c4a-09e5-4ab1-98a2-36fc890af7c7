
#ifndef APPSETTINGS_H
#define APPSETTINGS_H

#include <QtCore>

/**
 * @brief 应用程序设置单例类，负责管理全局配置（如服务器地址、语言、最近工程、调试模式等）
 *        支持配置的读取、写入和信号通知，便于全局访问和维护
 */
class AppSettings : public QObject {
    Q_OBJECT
public:
    /**
     * @brief 语言配置结构体，对应languages.config.json的每一项
     * @param desc  语言描述（如“中文 (中国)”）
     * @param alias  语言别名（如“zh_CN”），符合ISO标准
     */
    struct LanguageConfig {
        QString desc; ///< 语言描述
        QString alias; ///< 语言别名（如zh_CN）
        QString key; ///< key

        LanguageConfig() = default;
        LanguageConfig(const QString& key, const QString& desc, const QString& alias)
            : desc(desc)
            , alias(alias)
            , key(key)
        {
        }

        bool isValid() const { return !key.isEmpty() && !desc.isEmpty() && !alias.isEmpty(); }
    };

    typedef QMap<QString, LanguageConfig> LanguageMap; ///< 语言映射类型

public:
    /**
     * @brief 读取languages.config.json到m_languageMap
     * @param jsonPath 配置文件路径
     * @return 是否读取成功
     */
    bool loadLanguageConfig(const QString& jsonPath);

private:
    /**
     * @brief 构造函数（私有，单例模式）
     * @param parent 父对象
     */
    AppSettings(QObject* parent = nullptr);
    ~AppSettings();

public:
    /**
     * @brief 获取 AppSettings 单例对象
     * @return 单例引用
     */
    static AppSettings& instance();

public:
    /**
     * @brief 获取/设置服务器地址
     */
    QString serverHost() const;
    void setServerHost(const QString& newServerHost);

    /**
     * @brief 获取/设置是否使用中文
     */
    bool isUseChinese() const;
    // void setIsUseChinese(bool newIsUseChinese);

    /**
     * @brief 获取当前语言Key
     */
    QString currentLanguageKey() const;
    void setCurrentLanguageKey(const QString& key);

    /**
     * @brief 获取当前语言Key（符合ISO标准）
     *
     */
    QString currentLanguageStandrandKey() const;

    /**
     * @brief 获取当前语言信息
     *
     * @return LanguageConfig
     */
    LanguageConfig currentLanguageInfo() const;

    /**
     * @brief 获取所有语言信息
     *
     * @return QList<LanguageConfig>
     */
    QList<LanguageConfig> allLanguages() const;

    /**
     * @brief 获取/设置最近打开的工程列表
     */
    QVariantList recentProjects() const;
    void setRecentProjects(const QVariantList& newRecentProjects);
    /**
     * @brief 添加最近打开的工程
     * @param projectid 工程ID
     */
    void appendRecentProject(const QVariant& projectid);

    /**
     * @brief 获取/设置调试模式
     */
    bool isDebugMode() const;
    void setIsDebugMode(bool newIsDebugMode);

private:
    /**
     * @brief 读取配置
     */
    void readConfigs();
    /**
     * @brief 写入配置
     */
    void writeConfigs();

signals:
    /**
     * @brief 服务器地址变更信号
     */
    void serverHostChanged();
    /**
     * @brief 语言变更信号
     */
    void isUseChineseChanged();

private:
    QSettings* m_settings = nullptr; ///< 配置文件操作对象
    QString m_serverHost; ///< 服务器地址
    // bool m_isUseChinese; ///< 是否使用中文
    QVariantList m_recentProjects; ///< 最近打开的工程列表
    bool m_isDebugMode = false; ///< 是否调试模式

    QString m_currentLanguageKey; ///< 当前语言Key
    LanguageMap m_languageMap; ///< 语言映射类型
};

Q_DECLARE_METATYPE(AppSettings::LanguageConfig);

#endif // APPSETTINGS_H
