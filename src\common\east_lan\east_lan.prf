# 根据和架构判断

unix {
    contains(QT_ARCH, arm64) {
        message(" Linux/Unix arm64")
        LIBS += -L$$PWD/linux/arm64 -leastlanguagemanager
        INCLUDEPATH += $$PWD/linux/arm64
        HEADERS += $$PWD/linux/arm64/eastlanguagemanager.h
    } else:contains(QT_ARCH, x86_64) {
        message(" Linux/Unix x64")
        LIBS += -L$$PWD/linux/x64 -leastlanguagemanager
        INCLUDEPATH += $$PWD/linux/x64
        HEADERS += $$PWD/linux/x64/eastlanguagemanager.h
    } else {
        DEFINES += NO_EAST_LAN
    }
}
win32 {
    contains(QT_ARCH, x86_64) {
        message(" Windows x86_64")
        DEFINES += NO_EAST_LAN
    } else {
        message(" Windows ")
        DEFINES += NO_EAST_LAN
    }
}
