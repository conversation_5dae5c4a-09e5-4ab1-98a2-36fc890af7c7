#ifndef wsrpc_resp_funcs_h_
#define wsrpc_resp_funcs_h_

#include "wsrpcdefines.h"

namespace WSRPC {
inline QString errorCodeString(WSRPC::ErrorCode code, const QString& errorString)
{
    switch (code) {
    case WSRPC::ErrorCode::OtherError:
        return errorString;
    case WSRPC::ErrorCode::TimeOut: // 超时
        return QObject::tr("Calling WSRPC interface (%1) timed out").arg(errorString);
    case WSRPC::ErrorCode::InvaildMethodName: // 无效的方法名
        return QObject::tr("Failed to call WSRPC interface: Invalid method name");
    case WSRPC::ErrorCode::Disconnected: // 断开连接
        return QObject::tr("WSRPC server has disconnected");
    case WSRPC::ErrorCode::FailedToSendMessage: // 发送消息失败
        return QObject::tr("Failed to call WSRPC interface (%1): Failed to send message").arg(errorString);
    case WSRPC::ErrorCode::CouldNotConvertResultType: // 无法转换结果类型
        return QObject::tr("Failed to call WSRPC interface (%1): Result data type conversion failed").arg(errorString);
    case WSRPC::ErrorCode::CouldNotUnpackParam: // 无法解包参数
        return QObject::tr("Failed to call WSRPC interface (%1): Unable to unpack parameters").arg(errorString);
    case WSRPC::ErrorCode::CouldNotFoundService: // 未找到服务
        return QObject::tr("Failed to call WSRPC interface (%1): Unable to find service").arg(errorString);
    case WSRPC::ErrorCode::FailedToCallAPI: // 调用API失败
        return QObject::tr("Failed to call WSRPC interface (%1): Exception occurred while calling service interface").arg(errorString);
    }
    return "";
}
}
#endif
