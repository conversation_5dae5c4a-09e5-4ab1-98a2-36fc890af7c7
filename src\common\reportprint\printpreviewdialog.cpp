
/**
 * @file printpreviewdialog.cpp
 * @brief 打印预览对话框实现文件
 *
 * 实现模型、文件、PDF等多种内容的打印预览，支持自定义工具栏、窗口布局、内容类型自动识别。
 * 支持多平台兼容、PIMPL隐藏实现、与PrintHelper协作。
 */

#include "printpreviewdialog.h"
#include <QFileDialog>
#ifdef SUPPORT_PDF
#include <QPdfDocument>
#endif
#include <QPrinter>
#include <QApplication>
#include <QScreen>
#include <qtimer.h>
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
#include <QDesktopWidget>
#else
#include <QGuiApplication>
#include <QScreen>
#endif
#include <QPainter>
#include <QDebug>
#include <QSharedPointer>
#include <QScopedPointer>
#include <QAbstractItemModel>
#include "printhelper.h"
#include <QPrintPreviewWidget>
#include <QToolBar>

/**
 * @brief 打印预览对话框私有数据类（PIMPL）
 *
 * 管理PDF文档、模型、图片等待打印内容。
 */
class PrintPreviewDialogPrivate
{
public:
    explicit PrintPreviewDialogPrivate(QObject *parent = nullptr) {}
    ~PrintPreviewDialogPrivate(){}
#ifdef SUPPORT_PDF
    QSharedPointer<QPdfDocument> pdfDocument; ///< PDF文档指针
#endif
    QAbstractItemModel * model = nullptr;     ///< 打印用数据模型
    QPixmap pixmap;                          ///< 打印用图片
};


/**
 * @brief 构造函数
 * @param parent 父窗口
 *
 * 初始化打印预览窗口、工具栏、窗口大小、信号槽等。
 */
PrintPreviewDialog::PrintPreviewDialog(QWidget *parent)
    :QPrintPreviewDialog(new QPrinter(QPrinter::PrinterMode::HighResolution), parent),
    d_ptr(new PrintPreviewDialogPrivate(this))
{
    auto *previewWidget = findChild<QPrintPreviewWidget *>();
    if (previewWidget) {
        previewWidget->setZoomMode(QPrintPreviewWidget::CustomZoom);
        previewWidget->setZoomFactor(1);
        previewWidget->setOrientation(QPrinter::Orientation::Landscape);
    }
    // 获取顶部工具栏并自动切换横向
    auto *toolBar = findChild<QToolBar *>();
    auto actions = toolBar->actions();
    for(auto *a : std::as_const(actions)) {
        if(tr("Landscape") == a->text())
        {
            a->trigger();
        }
    }
    setWindowFlags(windowFlags() | Qt::WindowStaysOnTopHint);
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QRect screenGeometry = QApplication::desktop()->availableGeometry(this);
#else
    QRect screenGeometry = QApplication::primaryScreen()->availableGeometry();
#endif
    int width = screenGeometry.width();
    int height = screenGeometry.height();
    resize(width * 4 / 5, height * 4 / 5);
    move((width - this->width()) / 2, (height - this->height()) / 2);
    // 连接paintRequested信号到自定义槽
    connect(this, &QPrintPreviewDialog::paintRequested, this, &PrintPreviewDialog::onPaintRequested);
}


/**
 * @brief 静态接口：打印文件（支持PDF/图片）
 * @param fileName 文件路径
 *
 * 自动识别文件类型，加载PDF或图片，弹出预览对话框。
 */
void PrintPreviewDialog::printFile(const QString &fileName)
{
    QSharedPointer<PrintPreviewDialog> previewDialog = QSharedPointer<PrintPreviewDialog>::create();
    if (!fileName.isEmpty()) {
        if (fileName.endsWith(".pdf", Qt::CaseInsensitive)) {
#ifdef SUPPORT_PDF
            previewDialog->d_ptr->pdfDocument.reset(new QPdfDocument());
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
            if (previewDialog->d_ptr->pdfDocument->load(fileName) != QPdfDocument::NoError)
#else
            if (previewDialog->d_ptr->pdfDocument->load(fileName) != QPdfDocument::Error::None)
#endif
            {
                qWarning("Failed to load the PDF file.");
                return;
            }
            previewDialog->setWindowTitle(QObject::tr("Print Preview - %1").arg(fileName));
#else
            qWarning("PDF support is not enabled in this build.");
            return;
#endif // SUPPORT_PDF
        }
        else if (fileName.endsWith(".png", Qt::CaseInsensitive) ||
                 fileName.endsWith(".jpg", Qt::CaseInsensitive) ||
                 fileName.endsWith(".bmp", Qt::CaseInsensitive)) {
            previewDialog->d_ptr->pixmap.load(fileName);
            if (previewDialog->d_ptr->pixmap.isNull()) {
                qWarning("Failed to load the image file.");
            }
        }
        else {
            qWarning("Unsupported file format.");
            return;
        }
    }
    else
    {
        qWarning("No file specified for printing.");
        return;
    }
    // 显示预览对话框
    previewDialog->exec();
}


/**
 * @brief 静态接口：打印模型（如表格）
 * @param model 数据模型
 *
 * 绑定模型，弹出预览对话框，打印后释放全局数据。
 */
void PrintPreviewDialog::printModel(QAbstractItemModel *model)
{
    QSharedPointer<PrintPreviewDialog> previewDialog = QSharedPointer<PrintPreviewDialog>::create();
    if (model == nullptr) {
        qWarning("No model specified for printing.");
        return;
    }
    previewDialog->setWindowTitle(QObject::tr("Print Preview - Model"));
    for (int column = 0; column < model->columnCount(); ++column) {
        QVariant header = model->headerData(column, Qt::Horizontal, Qt::DisplayRole);
        // qDebug() << "列" << column << "表头:" << header.toString();
    }
    previewDialog->d_ptr->model = model;

    // 显示预览对话框
    previewDialog->exec();
    PrintHelper::releaseData();
}


/**
 * @brief 打印预览绘制槽
 * @param printer 打印机对象
 *
 * 根据内容类型自动选择PDF、图片或模型打印。
 */
void PrintPreviewDialog::onPaintRequested(QPrinter *printer)
{
    qDebug() << "Painting preview dialog...";
    // options.setScaledSize(pageSize);
#ifdef SUPPORT_PDF
    if (d_ptr->pdfDocument) {
        // 打印 PDF 文档
        PrintHelper::PrintPdf(printer, d_ptr->pdfDocument.data());
    }
#endif
    if (!d_ptr->pixmap.isNull()) {
        // 打印屏幕截图
        PrintHelper::printPixmap(printer, d_ptr->pixmap);
    }
    if (d_ptr->model) {
        // 打印模型数据
        PrintHelper::PrintModel(printer, d_ptr->model);
    }
}
