
/**
 * @file printhelper.cpp
 * @brief 打印辅助工具实现文件
 *
 * 实现表格、图片、PDF等内容的打印参数设置与打印功能，支持多平台兼容。
 * 支持自定义标题、表头颜色、边距、字体、对齐方式、列宽等。
 */

#include "printhelper.h"
#include <QPrinter>
#include <QPainter>
#include <QPixmap>
#include <QAbstractItemModel>
#include <QDebug>
#ifdef SUPPORT_PDF
#include <qpdfdocument.h>
#endif // SUPPORT_PDF
#include <QApplication>
#include <QStaticText>
#include <QTextLayout>
#include <QFont>


/**
 * @brief 打印参数与状态数据单例类（内部辅助）
 *
 * 管理全局打印参数，如字体、颜色、边距、列宽、标题等。
 * 提供粗体/常规字体、列宽计算、文本偏移等辅助方法。
 */
class PrintHelperData
{
public:
    static PrintHelperData *instance()
    {
        if (!m_instance) {
            m_instance = new PrintHelperData();
        }
        return m_instance;
    }
    static void release()
    {
        delete m_instance;
        m_instance = nullptr;
    }
    /**
     * @brief 获取粗体字体
     * @return QFont 粗体
     */
    QFont boldFont()
    {
        QFont tem = font;
        tem.setBold(true);
        tem.setPointSize(12); // 设置粗体字体大小
        return tem;
    }
    /**
     * @brief 获取常规字体
     * @return QFont 常规
     */
    QFont normalFont()
    {
        QFont tem = font;
        tem.setBold(false);
        return tem;
    }
    /**
     * @brief 按权重计算每列实际宽度
     * @param width 总宽度
     * @return 每列宽度数组
     */
    QVector<int> columnWidths(const int width)
    {
        QVector<int> widths;
        int totalWeight = std::accumulate(columnWidthsWeight.begin(), columnWidthsWeight.end(), 0);
        for (int weight : std::as_const(columnWidthsWeight)) {
            int colWidth = width * weight / totalWeight;
            widths.append(colWidth);
        }
        return widths;
    }
    /**
     * @brief 获取文本偏移量（考虑线宽）
     * @return 偏移点
     */
    QPoint textOffset() const
    {
        return _textOffset + QPoint(cellLineWidth / 2, cellLineWidth / 2); // 考虑单元格线条宽度
    }
    QColor headerColor = Qt::lightGray; ///< 表头颜色
    QTextOption textOption;              ///< 文本选项
    QMargins margins = {30, 30, 30, 30}; ///< 默认边距
    QPoint _textOffset = {10, 10};       ///< 文本偏移量
    QFont font;                         ///< 基础字体
    int cellLineWidth = 2;              ///< 单元格线条宽度
    QVector<int> columnWidthsWeight = {};// 列宽权重
    QString title = {};                 ///< 打印标题
private:
    PrintHelperData()
    {
        textOption.setWrapMode(QTextOption::WrapAtWordBoundaryOrAnywhere);
        textOption.setAlignment(Qt::AlignCenter);
        font = qApp->font();
        font.setPointSize(10);
        // title = qApp->applicationName();
    }
    static PrintHelperData *m_instance;
};
PrintHelperData *PrintHelperData::m_instance = nullptr;
#define HelperData PrintHelperData::instance()


/**
 * @brief PrintHelper 构造函数
 */
PrintHelper::PrintHelper() {}


/**
 * @brief 设置打印标题
 * @param title 标题字符串
 */
void PrintHelper::setTile(const QString &title)
{
    HelperData->title = title;
}


/**
 * @brief 设置表头颜色
 * @param color 颜色对象
 */
void PrintHelper::setHeaderColor(const QColor &color)
{
    HelperData->headerColor = color;
}


/**
 * @brief 设置页面边距
 * @param margins 边距对象
 */
void PrintHelper::setMargin(const QMargins &margins)
{
    HelperData->margins = margins;
}


/**
 * @brief 设置文本偏移量
 * @param offset 偏移点
 */
void PrintHelper::setTextOffset(const QPoint &offset)
{
    HelperData->_textOffset = offset;
}


/**
 * @brief 设置字体
 * @param font 字体对象
 */
void PrintHelper::setFont(const QFont &font)
{
    HelperData->font = font;
}


/**
 * @brief 设置单元格线宽
 * @param width 线宽
 */
void PrintHelper::setCellLineWidth(int width)
{
    if (width < 0) {
        qWarning("Cell line width cannot be negative, setting to default value of 2.");
        HelperData->cellLineWidth = 2;
    } else {
        HelperData->cellLineWidth = width;
    }
}


/**
 * @brief 设置列宽权重
 * @param columnWidths 列宽权重列表
 */
void PrintHelper::setcolWidthsWeights(const QList<int> &columnWidths)
{
    if (columnWidths.isEmpty()) {
        qWarning("Column widths cannot be empty, using default weights.");
        return;
    }
    HelperData->columnWidthsWeight.clear();
    for (int width : columnWidths) {
        if (width <= 0) {
            qWarning("Column width must be positive, using default weight of 15.");
            HelperData->columnWidthsWeight.append(15);
        } else {
            HelperData->columnWidthsWeight.append(width);
        }
    }
}


/**
 * @brief 设置文本对齐方式
 * @param alignment 对齐方式
 */
void PrintHelper::setAlignment(Qt::Alignment &alignment)
{
    HelperData->textOption.setAlignment(alignment);
}


/**
 * @brief 打印图片（QPixmap）
 * @param printer 打印机对象
 * @param pixmap 图片对象
 */
void PrintHelper::printPixmap(QPrinter *printer, const QPixmap &pixmap)
{
    if (pixmap.isNull()) {
        qWarning("No pixmap to print.");
        return;
    }
    QPainter painter(printer);
    // 获取打印页面的完整矩形尺寸
    QRect fullRect = printer->pageLayout().fullRectPixels(printer->resolution());
    qDebug() << "Full Rect:" << fullRect;
    // 绘制屏幕截图到整个页面
    painter.drawPixmap(fullRect, pixmap);
    painter.end();
}


/**
 * @brief 计算文本在指定宽度下的行数（自动换行）
 * @param text 文本内容
 * @param painter 绘图对象
 * @param width 可用宽度
 * @return 行数
 */
int calculateTextLineCount(const QString &text, const QPainter &painter, int width)
{
    if (text.isEmpty() || width <= 0)
        return 1; // 空文本也占一行
    QFontMetrics fontMetrics = painter.fontMetrics();

    int lineSpacing = fontMetrics.lineSpacing();
    qDebug() << "Line spacing:" << lineSpacing;
    int y = 0;

    QTextLayout textLayout(text, painter.font());
    textLayout.setTextOption(HelperData->textOption);
    textLayout.beginLayout();
    int height = 9999;
    if(auto *painterDevice = dynamic_cast<QPrinter *>(painter.device())) {
       height = painterDevice ->pageLayout().paintRectPixels((painterDevice->resolution())).height();
    }
    while (true) {
        QTextLine line = textLayout.createLine();

        if (!line.isValid())
            break;

        line.setLineWidth(width);
        const int nextLineY = y + lineSpacing;

        if (height >= nextLineY + lineSpacing) {
            y = nextLineY;
        } else {
            line = textLayout.createLine();
            break;
        }
    }
    textLayout.endLayout();
    return textLayout.lineCount();
}


/**
 * @brief 打印QAbstractItemModel（如表格模型）
 * @param printer 打印机对象
 * @param model 数据模型
 *
 * 支持自动分页、表头、标题、内容换行、列宽、边距、字体等参数。
 */
void PrintHelper::PrintModel(QPrinter *printer, QAbstractItemModel *model)
{
    if (!model) {
        qWarning("No model to print.");
        return;
    }

    QPainter painter(printer);
    // painter.setRenderHint(QPainter::Antialiasing);
    painter.setRenderHint(QPainter::TextAntialiasing);
    painter.setPen(QPen(Qt::black, HelperData->cellLineWidth));
    if (!painter.isActive()) {
        qWarning("Failed to start QPainter on printer.");
        return;
    }
    QMargins margins = HelperData->margins;

    // 获取页面尺寸
    QRect pageRect = printer->pageLayout().paintRectPixels(printer->resolution());

    int columnCount = model->columnCount();
    if(HelperData->columnWidthsWeight.isEmpty()) {
        HelperData->columnWidthsWeight.fill(10, columnCount); // 默认每列宽度权重为10
    }
    Q_ASSERT(columnCount == HelperData->columnWidthsWeight.size());

    int rowCount = model->rowCount();
    int boldFontLineSpacing = QFontMetrics(HelperData->boldFont(), printer).lineSpacing();
    int normalFontLineSpacing = QFontMetrics(HelperData->normalFont(), printer).lineSpacing();

    // 计算表头最大行数
    int rowMaxLines = 1;

    // 计算可用高度
    int availableHeight = pageRect.height() - margins.top() - margins.bottom();
    if (availableHeight <= 0) {
        qWarning("Available height is zero or negative, cannot print.");
        return;
    }
    // 计算可用宽度
    int availableWidth = pageRect.width() - margins.left() - margins.right();
    if (availableWidth <= 0) {
        qWarning("Available width is zero or negative, cannot print.");
        return;
    }
    /*
     * headerText: 表头文本
     * drawTexts: 绘制文本
     * dataText: 数据文本
     */
    QVarLengthArray<QString> headerText(columnCount), drawTexts(columnCount), dataText(columnCount);
    /*
     * drawCellSize: 绘制单元格尺寸
     * drawTextSize: 绘制文本尺寸
     */
    int titleHeight =0, headerHeight =0, dataHeight = 0;
    /*
     * freeHeight: 剩余可用高度
     */
    int freeHeight = availableHeight;

    /*
     * drawCellSize: 绘制单元格尺寸
     * drawTextSize: 绘制文本尺寸
     */
    QSize drawCellSize/*, drawTextSize*/;
    QVector<int> columnWidths = HelperData->columnWidths(availableWidth);
    availableWidth = std::accumulate(columnWidths.begin(), columnWidths.end(), 0);


    painter.setFont(HelperData->boldFont());
    if(!HelperData->title.isEmpty()) {
        int lines = calculateTextLineCount(HelperData->title, painter, availableWidth);
        titleHeight = boldFontLineSpacing * lines;
    }
    // 计算表头文本最大换行数
    for (int col = 0; col < columnCount; ++col) {
        headerText[col] = model->headerData(col, Qt::Horizontal).toString();
        drawCellSize.setWidth(columnWidths[col]);
        int drawTextWidth = drawCellSize.width() - HelperData->textOffset().x() * 2;
        int lines = calculateTextLineCount(headerText[col], painter, drawTextWidth);
        rowMaxLines = qMax(rowMaxLines, lines);
    }
    // 计算表头高度
    headerHeight = boldFontLineSpacing * rowMaxLines;

    QPoint leftTop(margins.left(), margins.top());
    QPoint leftTopOffset = leftTop;

    // 绘制行
    auto drawDataRow = [&]() {
        for (int col = 0; col < columnCount; ++col) {
            drawCellSize.setWidth(columnWidths[col]);
            painter.drawLine(leftTopOffset, leftTopOffset + QPoint(drawCellSize.width(), 0));
            painter.drawLine(leftTopOffset, leftTopOffset + QPoint(0, drawCellSize.height()));
            QRect cellRect(leftTopOffset, drawCellSize);
            cellRect.adjust(HelperData->textOffset().x(), HelperData->textOffset().y(), -HelperData->textOffset().x(), -HelperData->textOffset().y());
            painter.drawText(cellRect, drawTexts[col], HelperData->textOption);
            leftTopOffset += QPoint(drawCellSize.width(), 0); // 向右移动到下一个单元格
        }
        painter.drawLine(leftTopOffset, leftTopOffset + QPoint(0, drawCellSize.height()));
        leftTopOffset += QPoint(0, drawCellSize.height()); // 向下移动到下一行
        freeHeight -= drawCellSize.height();
        // 重置当前左侧位置
        leftTopOffset.setX(leftTop.x());
    };

    auto drawTitle = [&]() {
        if (HelperData->title.isEmpty()) {
            return; // 如果没有标题,则不绘制
        }
        drawCellSize.setHeight(titleHeight + HelperData->textOffset().y() * 2); // 单元格高度 = 文本高度 + 上下偏移量
        drawCellSize.setWidth(availableWidth);
        painter.save();
        painter.setFont(HelperData->boldFont());
        painter.drawLine(leftTopOffset, leftTopOffset + QPoint(drawCellSize.width(), 0));
        painter.drawLine(leftTopOffset, leftTopOffset + QPoint(0, drawCellSize.height()));

        QRect cellRect(leftTopOffset, drawCellSize);
        cellRect.adjust(HelperData->textOffset().x(), HelperData->textOffset().y(), -HelperData->textOffset().x(), -HelperData->textOffset().y());
        painter.drawText(cellRect, HelperData->title, HelperData->textOption);
        leftTopOffset += QPoint(drawCellSize.width(), 0);
        painter.drawLine(leftTopOffset, leftTopOffset + QPoint(0, drawCellSize.height()));
        painter.restore();
        leftTopOffset = leftTop + QPoint(0, drawCellSize.height()); // 向下移动到下一行
        freeHeight -= drawCellSize.height(); // 减去表头高度
    };
    // 绘制表头
    auto drawHeader = [&]() {
        if(leftTop != leftTopOffset) {
            painter.drawLine(leftTopOffset, leftTopOffset + QPoint(availableWidth, 0));
            printer->newPage();
            leftTopOffset = leftTop; // 重置当前顶部位置
            freeHeight = availableHeight; // 重置可用高度
        }
        drawTitle();
        drawTexts = headerText;

        drawCellSize.setHeight(headerHeight + HelperData->textOffset().y() * 2); // 单元格高度 = 文本高度 + 上下偏移量
        painter.save();
        painter.setBrush(HelperData->headerColor);
        painter.setFont(HelperData->boldFont());
        drawDataRow();
        painter.restore();
    };
    // 绘制数据行
    auto drawData = [&]() {
        drawTexts = dataText;

        // drawTextSize.setHeight(dataHeight);
        drawCellSize.setHeight(dataHeight); // 单元格高度 = 文本高度 + 上下偏移量
        painter.save();
        painter.setFont(HelperData->normalFont());
        drawDataRow();
        painter.restore();
    };
    drawHeader();

    for (int row = 0; row < rowCount; ++row) {
        rowMaxLines = 1;
        painter.setFont(HelperData->normalFont());
        // 计算行数据文本最大换行数
        for (int col = 0; col < columnCount; ++col) {
            dataText[col] = model->data(model->index(row, col), Qt::DisplayRole).toString();
            int drawTextWidth = columnWidths[col] - HelperData->textOffset().x() * 2;
            int lines = calculateTextLineCount(dataText[col], painter, drawTextWidth);
            rowMaxLines = qMax(rowMaxLines, lines);
        }
        // 计算行数据高度
        dataHeight = normalFontLineSpacing * rowMaxLines + HelperData->textOffset().y() * 2; // 单元格高度 = 文本高度 + 上下偏移量
        // 如果当前行数据高度超过页面总高度,则无法打印,请调整数据或打印设置
        if (availableHeight < dataHeight + headerHeight) {
            qWarning("Data height exceeds available height, please adjust your data or printer settings.");
            return;
        }
        if (dataHeight > freeHeight) {
            drawHeader();
        }
        drawData();
    }
    // 绘制最后一行边框
    if (leftTopOffset != leftTop) {
        painter.drawLine(leftTopOffset, leftTopOffset + QPoint(availableWidth, 0));
    }
    painter.end();
}

#ifdef SUPPORT_PDF
/**
 * @brief 打印PDF文档
 * @param printer 打印机对象
 * @param pdfDocument PDF文档对象
 *
 * 支持多页PDF高分辨率渲染与打印。
 */
void PrintHelper::PrintPdf(QPrinter *printer,  QPdfDocument *pdfDocument)
{
    if (!pdfDocument) {
        qWarning("No PDF document to print.");
        return;
    }
    QPainter painter(printer);
    // 获取打印页面的完整矩形尺寸
    int dpi = printer->resolution();
    QSize pageSize = printer->pageLayout().pageSize().sizePixels(dpi);
    qDebug() << "Page size in pixels:" << pageSize << ", DPI:" << dpi;
    // 遍历 PDF 的每一页
    for (int i = 0; i < pdfDocument->pageCount(); i++) {
        // 高分辨率渲染 PDF 页面
        auto img = pdfDocument->render(i, pageSize);
        painter.drawImage(0,0, img);
        qDebug() << "Rendered page:" << i + 1 << ", size:" << img.size();
    }
    pdfDocument->close();
    painter.end();
}
#endif // SUPPORT_PDF


/**
 * @brief 释放打印相关全局数据
 */
void PrintHelper::releaseData()
{
    PrintHelperData::release();
    qDebug() << "PrintHelper data released.";
}
