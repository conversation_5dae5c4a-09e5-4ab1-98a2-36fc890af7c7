[Begin] SSD-MONITOR-001 VariableMonitorThread 变量监控线程类
职责：负责变量在线值的监控管理，包括批量获取在线值、变量对比、监控状态管理、定时器控制等功能。
对外接口：
1) 函数名：VariableMonitorThread(WSRPC::IEndPoint* endpoint, QObject* parent = nullptr);
函数功能：构造函数，初始化变量监控线程。
输入：WSRPC::IEndPoint* endpoint：WebSocket RPC端点；
QObject* parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：GlobalStructs::BatchOnlineValues& batchOnlineValues();
函数功能：获取批量获取在线值参数。
输入：无。
输出：无。
返回值：GlobalStructs::BatchOnlineValues&：批量在线值参数引用。
3) 函数名：GlobalStructs::CompareInfoList& compareInfos();
函数功能：获取对比信息列表。
输入：无。
输出：无。
返回值：GlobalStructs::CompareInfoList&：对比信息列表引用。
4) 函数名：void setCompareInfos(const GlobalStructs::CompareInfoList& newCompareInfos);
函数功能：设置对比信息列表。
输入：const GlobalStructs::CompareInfoList& newCompareInfos：新的对比信息列表。
输出：无。
返回值：无。
5) 函数名：GlobalStructs::MultiStationCompareInfoList& multiCompareInfos();
函数功能：获取多站对比信息列表。
输入：无。
输出：无。
返回值：GlobalStructs::MultiStationCompareInfoList&：多站对比信息列表引用。
6) 函数名：void setMultiCompareInfos(const GlobalStructs::MultiStationCompareInfoList& newMultiCompareInfos);
函数功能：设置多站对比信息列表。
输入：const GlobalStructs::MultiStationCompareInfoList& newMultiCompareInfos：新的多站对比信息列表。
输出：无。
返回值：无。
7) 函数名：GlobalStructs::ResponseCode startVarsMonit(const std::string& commid, const std::string& language);
函数功能：启动变量监控。
输入：const std::string& commid：通信ID；
const std::string& language：语言设置。
输出：无。
返回值：GlobalStructs::ResponseCode：响应状态码。
8) 函数名：void stopVarsMonit(const QString& language, WSRPC::ProgressCallBack progressCallBack, double progress);
函数功能：停止变量监控。
输入：const QString& language：语言设置；
WSRPC::ProgressCallBack progressCallBack：进度回调函数；
double progress：进度值。
输出：无。
返回值：无。
9) 函数名：bool compareInfoByIndex(int id, GlobalStructs::CompareInfo& compareInfo);
函数功能：根据索引获取对比信息。
输入：int id：索引；
GlobalStructs::CompareInfo& compareInfo：返回的对比信息。
输出：GlobalStructs::CompareInfo& compareInfo：对比信息。
返回值：bool：获取成功返回true，否则返回false。
10) 函数名：GlobalStructs::ForceInfo genForceInfo() const;
函数功能：生成对比表强制信息。
输入：无。
输出：无。
返回值：GlobalStructs::ForceInfo：对比表强制信息。
[End]

[Begin] SSD-MONITOR-002 MonitorPlatformStatus 平台状态监控类
职责：负责平台状态的监控管理，包括平台错误检测、定时器监控、连接状态管理、错误处理等功能。
对外接口：
1) 函数名：MonitorPlatformStatus(QObject *parent = nullptr);
函数功能：构造函数，初始化平台状态监控。
输入：QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void onStartmonitorPlatformError(const unsigned int& socketFlag);
函数功能：启动平台错误监控。
输入：const unsigned int& socketFlag：套接字标识。
输出：无。
返回值：无。
3) 函数名：void onStopMonitorPlatformStatus();
函数功能：停止平台状态监控。
输入：无。
输出：无。
返回值：无。
4) 函数名：void onTimerMonitor();
函数功能：定时器监控处理。
输入：无。
输出：无。
返回值：无。
5) 函数名：void sigPlatformError();
函数功能：平台错误信号。
输入：无。
输出：无。
返回值：无。
6) 函数名：void sigUnlinkOver();
函数功能：断开连接完成信号。
输入：无。
输出：无。
返回值：无。
[End]

[Begin] SSD-MONITOR-003 TestPluginManage 测试插件管理类
职责：负责测试插件的管理，包括插件加载、文件监视、插件缓存、平台类型管理等功能。
对外接口：
1) 函数名：TestPluginManage& getInstance();
函数功能：获取单例实例。
输入：无。
输出：无。
返回值：TestPluginManage&：单例实例引用。
2) 函数名：TestPluginManage(QObject *parent = nullptr);
函数功能：构造函数，初始化测试插件管理。
输入：QObject *parent：父对象指针。
输出：无。
返回值：无。
3) 函数名：void loadTestPlugins();
函数功能：加载测试插件。
输入：无。
输出：无。
返回值：无。
4) 函数名：bool loadPluginObject(const QFileInfo &fileInfo);
函数功能：加载指定插件对象。
输入：const QFileInfo &fileInfo：插件文件信息。
输出：无。
返回值：bool：加载成功返回true，否则返回false。
[End]

[Begin] SSD-SYNC-001 SynchronizeCondition 同步条件类
职责：负责同步条件的管理，包括条件加载、延时控制、偏差设置、步骤装配等功能。
对外接口：
1) 函数名：SynchronizeCondition();
函数功能：构造函数，初始化同步条件。
输入：无。
输出：无。
返回值：无。
2) 函数名：virtual bool load(const QDomElement& element);
函数功能：从XML元素加载同步条件。
输入：const QDomElement& element：XML元素。
输出：无。
返回值：bool：加载成功返回true，否则返回false。
3) 函数名：int delayTimeMs() const;
函数功能：获取延时时间（毫秒）。
输入：无。
输出：无。
返回值：int：延时时间。
4) 函数名：float deviation() const;
函数功能：获取偏差值。
输入：无。
输出：无。
返回值：float：偏差值。
5) 函数名：bool assemblyOneStep(const stAssemblyInfo& assinfo, const QString &language, QString *error, int &delay);
函数功能：装配一个步骤。
输入：const stAssemblyInfo& assinfo：装配信息；
const QString &language：语言设置；
QString *error：错误信息；
int &delay：延时时间。
输出：QString *error：错误信息；
int &delay：延时时间。
返回值：bool：装配成功返回true，否则返回false。
[End]

[Begin] SSD-SYNC-002 SyncStyledDelegate 同步样式代理类
职责：负责表格中比对结果的高亮显示，包括不一致行/一致行管理、颜色设置、批量操作等功能。
对外接口：
1) 函数名：SyncStyledDelegate(QObject* parent);
函数功能：构造函数，初始化同步样式代理。
输入：QObject* parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void addNotSameRow(int row);
函数功能：添加不一致行。
输入：int row：行号。
输出：无。
返回值：无。
3) 函数名：void removeNotSameRow(int row);
函数功能：移除不一致行。
输入：int row：行号。
输出：无。
返回值：无。
4) 函数名：void setNotSameRows(const QList<int>& row);
函数功能：批量设置不一致行。
输入：const QList<int>& row：行号列表。
输出：无。
返回值：无。
5) 函数名：QList<int> notSameRows() const;
函数功能：获取所有不一致行。
输入：无。
输出：无。
返回值：QList<int>：行号列表。
6) 函数名：void clear();
函数功能：清除所有高亮行。
输入：无。
输出：无。
返回值：无。
7) 函数名：void setNotSameColor(const QColor& color);
函数功能：设置不一致行高亮颜色。
输入：const QColor& color：颜色。
输出：无。
返回值：无。
8) 函数名：void propertyChanged();
函数功能：属性变更信号。
输入：无。
输出：无。
返回值：无。
[End]

[Begin] SSD-MONITOR-004 HwProDeal 硬接线工程处理类
职责：负责硬接线工程的处理管理，包括文件系统监视、工程数据处理、用例文件管理、平台哈希值检查等功能。
对外接口：
1) 函数名：HwProDeal(const uint& socketFlag, QObject *parent = nullptr);
函数功能：构造函数，初始化硬接线工程处理。
输入：const uint& socketFlag：套接字标识；
QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void onDirectoryChanged(const QString& path);
函数功能：目录变化处理。
输入：const QString& path：变化的目录路径。
输出：无。
返回值：无。
3) 函数名：void onRemoveInvalidUsecaseFile(QString usecasePath);
函数功能：移除无效用例文件。
输入：QString usecasePath：用例文件路径。
输出：无。
返回值：无。
4) 函数名：void initData();
函数功能：初始化数据。
输入：无。
输出：无。
返回值：无。
5) 函数名：CaseDefine::TestType getTestType(QString testTypeName);
函数功能：获取试验类型。
输入：QString testTypeName：试验类型名称。
输出：无。
返回值：CaseDefine::TestType：试验类型。
[End]

[Begin] SSD-MONITOR-005 PtProDeal 定期试验工程处理类
职责：负责定期试验工程的处理管理，包括文件系统监视、HTTP API调用、工程版本管理、用例文件处理等功能。
对外接口：
1) 函数名：PtProDeal(const uint& socketFlag, QObject *parent = nullptr);
函数功能：构造函数，初始化定期试验工程处理。
输入：const uint& socketFlag：套接字标识；
QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void onDirectoryChanged(const QString& path);
函数功能：目录变化处理。
输入：const QString& path：变化的目录路径。
输出：无。
返回值：无。
3) 函数名：void onRemoveInvalidUsecaseFile(QString usecasePath);
函数功能：移除无效用例文件。
输入：QString usecasePath：用例文件路径。
输出：无。
返回值：无。
4) 函数名：void initData();
函数功能：初始化数据。
输入：无。
输出：无。
返回值：无。
[End]

[Begin] SSD-SYNC-003 XlsxDispatchThread Excel解析调度线程类
职责：负责Excel文件的解析调度管理，包括用例数据读取、配置解析、条件表处理、数据恢复等功能。
对外接口：
1) 函数名：XlsxDispatchThread(uint socketFlag, QObject *parent = nullptr);
函数功能：构造函数，初始化Excel解析调度线程。
输入：uint socketFlag：套接字标识；
QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void onDealReadPeriodicAllXlsx(QString usecasePath);
函数功能：处理读取定期试验所有Excel文件。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
3) 函数名：void onGetUsecaseInfo(QString usecasePath, XlsxDataType xlsxDataType);
函数功能：获取用例信息。
输入：QString usecasePath：用例路径；
XlsxDataType xlsxDataType：Excel数据类型。
输出：无。
返回值：无。
4) 函数名：void readTestConfig(QString usecasePath);
函数功能：读取测试配置。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
5) 函数名：void readForcedCondition(QString usecasePath);
函数功能：读取强制条件。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
6) 函数名：void readTestCondition(QString usecasePath);
函数功能：读取测试条件。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
7) 函数名：void readDataRecover(QString usecasePath);
函数功能：读取数据恢复。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
8) 函数名：void readUsecase(QString usecasePath);
函数功能：读取用例。
输入：QString usecasePath：用例路径。
输出：无。
返回值：无。
[End]

[Begin] SSD-SYNC-004 ServiceManage 服务管理类
职责：负责服务的综合管理，包括试验流程控制、数据同步、信号处理、线程管理、WebSocket RPC服务等功能。
对外接口：
1) 函数名：ServiceManage(QObject *parent = nullptr);
函数功能：构造函数，初始化服务管理。
输入：QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：int getSocketFlag();
函数功能：获取套接字标识。
输入：无。
输出：无。
返回值：int：套接字标识。
3) 函数名：void onReciveDataRecoverData(HeadDefine::DataRecover_Items headItem, CaseDefine::DataRecoverData datarecoverData);
函数功能：接收数据恢复数据。
输入：HeadDefine::DataRecover_Items headItem：数据恢复头项；
CaseDefine::DataRecoverData datarecoverData：数据恢复数据。
输出：无。
返回值：无。
4) 函数名：void initTestFlow();
函数功能：初始化测试流程。
输入：无。
输出：无。
返回值：无。
5) 函数名：void initXlxsParse();
函数功能：初始化Excel解析。
输入：无。
输出：无。
返回值：无。
6) 函数名：void onReadUsecaseXlsxData(QString useCasePah, unsigned int socketFlag);
函数功能：读取用例Excel数据。
输入：QString useCasePah：用例路径；
unsigned int socketFlag：套接字标识。
输出：无。
返回值：无。
[End]

[Begin] SSD-SYNC-005 TimerThreadBase 定时器线程基类
职责：负责定时器线程的基础管理，包括定时器启动停止、超时处理、线程安全控制等功能。
对外接口：
1) 函数名：TimerThreadBase(QObject* parent = nullptr);
函数功能：构造函数，初始化定时器线程基类。
输入：QObject* parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：virtual void startTimer(int intervalMs);
函数功能：启动定时器。
输入：int intervalMs：定时器间隔（毫秒）。
输出：无。
返回值：无。
3) 函数名：virtual void stopTimer();
函数功能：停止定时器。
输入：无。
输出：无。
返回值：无。
4) 函数名：virtual void onTimeOut() = 0;
函数功能：定时器超时处理（纯虚函数）。
输入：无。
输出：无。
返回值：无。
5) 函数名：bool isTimerActive() const;
函数功能：检查定时器是否激活。
输入：无。
输出：无。
返回值：bool：定时器是否激活。
[End]

[Begin] SSD-MONITOR-006 FileSystemWatcher 文件系统监视器类
职责：负责文件系统的监视管理，包括目录监视、文件变化检测、路径管理、信号通知等功能。
对外接口：
1) 函数名：QFileSystemWatcher(QObject *parent = nullptr);
函数功能：构造函数，初始化文件系统监视器。
输入：QObject *parent：父对象指针。
输出：无。
返回值：无。
2) 函数名：void addPath(const QString &path);
函数功能：添加监视路径。
输入：const QString &path：要监视的路径。
输出：无。
返回值：无。
3) 函数名：void removePath(const QString &path);
函数功能：移除监视路径。
输入：const QString &path：要移除的路径。
输出：无。
返回值：无。
4) 函数名：void directoryChanged(const QString &path);
函数功能：目录变化信号。
输入：const QString &path：变化的目录路径。
输出：无。
返回值：无。
5) 函数名：void fileChanged(const QString &path);
函数功能：文件变化信号。
输入：const QString &path：变化的文件路径。
输出：无。
返回值：无。
6) 函数名：QStringList directories() const;
函数功能：获取监视的目录列表。
输入：无。
输出：无。
返回值：QStringList：目录列表。
7) 函数名：QStringList files() const;
函数功能：获取监视的文件列表。
输入：无。
输出：无。
返回值：QStringList：文件列表。
[End]
