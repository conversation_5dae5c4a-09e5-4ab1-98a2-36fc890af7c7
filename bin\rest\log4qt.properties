logpath=./rest_logs

log4j.reset=true
log4j.Debug=INFO
log4j.threshold=ALL
log4j.handleQtMessages=true
log4j.watchThisFile=false

log4j.rootLogger=ALL, console, daily

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.target=STDOUT_TARGET
log4j.appender.console.layout=org.apache.log4j.TTCCLayout
log4j.appender.console.layout.dateFormat=[MM-dd hh:mm:ss]
log4j.appender.console.layout.contextPrinting=false
log4j.appender.console.threshold=ALL
log4j.appender.console.encoding=UTF-8

log4j.appender.daily=org.apache.log4j.DailyFileAppender
log4j.appender.daily.file=${logpath}/rest.log
log4j.appender.daily.appendFile=true
log4j.appender.daily.datePattern=_yyyy_MM_dd
log4j.appender.daily.keepDays=90
log4j.appender.daily.layout=${log4j.appender.console.layout}
log4j.appender.daily.layout.dateFormat=${log4j.appender.console.layout.dateFormat}
log4j.appender.daily.layout.contextPrinting=${log4j.appender.console.layout.contextPrinting}
