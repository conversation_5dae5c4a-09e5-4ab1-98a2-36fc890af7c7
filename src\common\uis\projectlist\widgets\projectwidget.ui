<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProjectWidget</class>
 <widget class="QWidget" name="ProjectWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>687</width>
    <height>78</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <item row="0" column="1" rowspan="6">
    <widget class="QToolButton" name="toolButton">
     <property name="text">
      <string/>
     </property>
     <property name="icon">
      <iconset resource="../../../../themes/themes.qrc">
       <normaloff>:/styles/default/images/project.png</normaloff>:/styles/default/images/project.png</iconset>
     </property>
     <property name="iconSize">
      <size>
       <width>40</width>
       <height>40</height>
      </size>
     </property>
     <property name="toolButtonStyle">
      <enum>Qt::ToolButtonIconOnly</enum>
     </property>
    </widget>
   </item>
   <item row="1" column="2">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
   <item row="0" column="0" rowspan="6">
    <widget class="QFrame" name="frameBlock">
     <property name="maximumSize">
      <size>
       <width>24</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleclass" stdset="0">
      <string notr="true">label-project-item-left-block</string>
     </property>
    </widget>
   </item>
   <item row="5" column="2">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="3" column="2">
    <widget class="QLabel" name="labelDateTime">
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <spacer name="verticalSpacer_2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="2" column="2">
    <spacer name="verticalSpacer_3">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../../themes/themes.qrc"/>
 </resources>
 <connections/>
</ui>
