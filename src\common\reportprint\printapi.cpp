
/**
 * @file printapi.cpp
 * @brief 打印API实现文件
 *
 * 实现A4报表打印、打印预览、内容HTML生成、页面设置等功能。
 * 支持横纵向、列宽、内容高亮、历史记录标记等。
 */

#if _MSC_VER >= 1600
#pragma execution_character_set("utf-8")
#endif

#include "printapi.h"                  // 对应头文件
#include <QApplication>
#include <QDesktopServices>
#include <QScreen>
#include <QTextDocument>
#include <QtPrintSupport/QPrintPreviewDialog>


// 静态成员初始化
PrintAPI* PrintAPI::_instance = 0;

/**
 * @brief 构造函数（私有，单例模式）
 * @param parent 父对象
 */
PrintAPI::PrintAPI(QObject* parent)
    : QObject(parent)
{
}


/**
 * @brief 打印A4报表（主接口）
 * @param title      报表主标题
 * @param subTitle   报表副标题
 * @param columnNames 列名列表
 * @param columnWidths 列宽列表
 * @param content    内容（每行一个字符串，分号分隔）
 * @param landscape  是否横向打印
 * @param check      是否高亮历史记录
 * @param pageSize   页面尺寸，默认A4
 *
 * 生成HTML表格，支持主副标题、列宽、内容高亮，弹出打印预览。
 */
void PrintAPI::PrintA4(const QString& title, const QString& subTitle, const QList<QString>& columnNames,
    const QList<int>& columnWidths, const QStringList& content, bool landscape, bool check,
    QPageSize pageSize)
{
    Q_ASSERT(columnNames.size() == columnWidths.size());

    QList<double> columnPercentWidths;
    const int sum = std::accumulate(columnWidths.begin(), columnWidths.end(), 0);
    for (int i = 0; i < columnWidths.size(); i++) {
        columnPercentWidths << ((double)columnWidths[i] / (double)sum) * 100;
    }

    // 计算行数列数
    int columnCount = columnNames.count();
    int rowCount = content.count();

    // 清空原有数据,确保每次都是新的数据
    html.clear();

    // 表格开始
    html.append("<table border='0.5' cellspacing='0' cellpadding='3'>");

    // 标题占一行,居中显示
    if (title.length() > 0) {
        html.append(QString("<tr><td align='center' style='vertical-align:middle;font-weight:bold;' colspan='%1'>")
                .arg(columnCount));
        html.append(title);
        html.append("</td></tr>");
    }

    // 副标题占一行,左对齐显示
    if (subTitle.length() > 0) {
        html.append(QString("<tr><td align='left' style='vertical-align:middle;' colspan='%1'>")
                .arg(columnCount));
        html.append(subTitle);
        html.append("</td></tr>");
    }

    // 循环写入字段名,字段名占一行,居中显示
    if (columnCount > 0) {
        html.append("<tr>");
        for (int i = 0; i < columnCount; i++) {
            html.append(QString("<td width='%1%' bgcolor='lightgray' align='center' style='vertical-align:middle;'>")
                    .arg(columnPercentWidths.at(i)));
            html.append(columnNames.at(i));
            html.append("</td>");
        }
        html.append("</tr>");
    }

    // 循环一行行构建数据
    for (int i = 0; i < rowCount; i++) {
        QStringList value = content.at(i).split(";");
        html.append("<tr>");

        if (check) {
            // 历史记录高亮（红色）
            if (value.at(value.count() - 1) == "历史记录") {
                for (int j = 0; j < columnCount; j++) {
                    html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>"
                                        "<font color='red'>")
                            .arg(columnWidths.at(j)));
                    html.append(value.at(j));
                    html.append("</font></td>");
                }
            } else {
                for (int j = 0; j < columnCount; j++) {
                    html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>")
                            .arg(columnWidths.at(j)));
                    html.append(value.at(j));
                    html.append("</td>");
                }
            }
        } else {
            for (int j = 0; j < columnCount; j++) {
                html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>")
                        .arg(columnWidths.at(j)));
                html.append(value.at(j));
                html.append("</td>");
            }
        }

        html.append("</tr>");
    }

    html.append("</table>");

    // 调用打印机打印
    QPrinter printer;
    // 设置输出格式
    printer.setOutputFormat(QPrinter::NativeFormat);
    // 设置纸张规格
    printer.setPageSize(pageSize);
    // 设置横向纵向及页边距
    if (landscape) {
        printer.setPageOrientation(QPageLayout::Landscape);
        printer.setPageMargins(QMarginsF(10, 10, 10, 12), QPageLayout::Millimeter);
    } else {
        printer.setPageOrientation(QPageLayout::Portrait);
        printer.setPageMargins(QMarginsF(10, 10, 10, 16), QPageLayout::Millimeter);
    }
    if(printDialog){
        printDialog->close();
    }

    // 创建打印预览对话框
    printDialog = QSharedPointer<QPrintPreviewDialog>::create();
    connect(printDialog.data(), SIGNAL(paintRequested(QPrinter*)), this, SLOT(printView(QPrinter*)));
    printDialog->setWindowTitle(tr("Print Preview"));
    // printDialog.setGeometry(qApp->primaryScreen()->availableGeometry());
    printDialog->show();
}

// void PrintAPI::PrintA4(const QString& title, const QList<QString>& columnNames, const QList<int>& columnWidths,
//     const QStringList& subTitle1, const QStringList& subTitle2,
//     const QStringList& content, bool landscape, bool check,
//     QPageSize pageSize)
// {
//     Q_ASSERT(columnNames.size() == columnWidths.size());

//     QList<double> columnPercentWidths;
//     const int sum = std::accumulate(columnWidths.begin(), columnWidths.end(), 0);
//     for (int i = 0; i < columnWidths.size(); i++) {
//         columnPercentWidths << ((double)columnWidths[i] / (double)sum) * 100;
//     }

//     // 计算列数
//     int columnCount = columnNames.count();

//     // 清空原有数据,确保每次都是新的数据
//     html.clear();

//     // 表格开始
//     html.append("<table border='0.5' cellspacing='0' cellpadding='3'>");

//     // 标题占一行,居中显示
//     if (title.length() > 0) {
//         html.append(QString("<tr><td align='center' style='vertical-align:middle;font-weight:bold;' colspan='%1'>")
//                 .arg(columnCount));
//         html.append(title);
//         html.append("</td></tr>");
//     }

//     // 循环添加副标题/字段名/内容
//     int count = content.count();
//     for (int i = 0; i < count; i++) {
//         // 加个空行隔开
//         html.append(QString("<tr><td colspan='%1'>").arg(columnCount));
//         html.append("</td></tr>");

//         // 副标题1
//         if (subTitle1.count() > 0 && subTitle1.count() > i) {
//             if (subTitle1.at(i).length() > 0) {
//                 html.append(QString("<tr><td align='left' style='vertical-align:middle;' colspan='%1'>")
//                         .arg(columnCount));
//                 html.append(subTitle1.at(i));
//                 html.append("</td></tr>");
//             }
//         }

//         // 副标题2
//         if (subTitle2.count() > 0 && subTitle2.count() > i) {
//             if (subTitle2.at(i).length() > 0) {
//                 html.append(QString("<tr><td align='left' style='vertical-align:middle;' colspan='%1'>")
//                         .arg(columnCount));
//                 html.append(subTitle2.at(i));
//                 html.append("</td></tr>");
//             }
//         }

//         // 逐个添加字段名称
//         if (columnCount > 0) {
//             html.append("<tr>");
//             for (int i = 0; i < columnCount; i++) {
//                 html.append(QString("<td width='%1%' bgcolor='lightgray' align='center' style='vertical-align:middle;'>")
//                         .arg(columnPercentWidths.at(i)));
//                 html.append(columnNames.at(i));
//                 html.append("</td>");
//             }
//             html.append("</tr>");
//         }

//         QStringList list = content.at(i).split(";");

//         // 逐个添加数据
//         int rowCount = list.count();
//         for (int j = 0; j < rowCount; j++) {
//             html.append("<tr>");

//             QString temp = list.at(j);
//             QStringList value = temp.split("|");
//             int valueCount = value.count();

//             if (check) {
//                 // 如果是历史记录则文字红色
//                 if (value.at(valueCount - 1) == "1") {
//                     for (int k = 0; k < valueCount - 1; k++) {
//                         html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>"
//                                             "<font color='red'>")
//                                 .arg(columnWidths.at(k)));
//                         html.append(value.at(k));
//                         html.append("</font></td>");
//                     }
//                 } else {
//                     for (int k = 0; k < valueCount - 1; k++) {
//                         html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>")
//                                 .arg(columnWidths.at(k)));
//                         html.append(value.at(k));
//                         html.append("</td>");
//                     }
//                 }
//             } else {
//                 for (int k = 0; k < valueCount; k++) {
//                     html.append(QString("<td width='%1' align='center' style='vertical-align:middle;'>")
//                             .arg(columnWidths.at(k)));
//                     html.append(value.at(k));
//                     html.append("</td>");
//                 }
//             }

//             html.append("</tr>");
//         }
//     }

//     html.append("</table>");

//     // 调用打印机打印
//     QPrinter printer;
//     // 设置输出格式
//     printer.setOutputFormat(QPrinter::NativeFormat);
//     // 设置纸张规格
//     printer.setPageSize(pageSize);
//     // 设置横向纵向及页边距
//     if (landscape) {
//         printer.setPageOrientation(QPageLayout::Landscape);
//         printer.setPageMargins(QMarginsF(10, 10, 10, 12), QPageLayout::Millimeter);
//     } else {
//         printer.setPageOrientation(QPageLayout::Portrait);
//         printer.setPageMargins(QMarginsF(10, 10, 10, 16), QPageLayout::Millimeter);
//     }

//     printDialog.setWindowTitle(tr("Print Preview"));
//     connect(&printDialog, SIGNAL(paintRequested(QPrinter*)), this, SLOT(printView(QPrinter*)));
//     printDialog.setGeometry(qApp->primaryScreen()->availableGeometry());
//     printDialog.show();
// }


/**
 * @brief 打印视图槽函数
 * @param printer 打印机对象
 *
 * 将HTML内容渲染为文档并输出到打印机。
 */
void PrintAPI::printView(QPrinter* printer)
{
    QTextDocument txt;
    txt.setDefaultStyleSheet(R"(
    <style>
      table {
        width: 100%;
        table-layout: fixed;
        word-wrap: break-word;
      }
      td {
        word-break: break-all;
      }
    </style>
    )");
    txt.setPageSize(printer->pageRect().size());
    txt.setHtml(html.join(""));
    txt.print(printer);
}
