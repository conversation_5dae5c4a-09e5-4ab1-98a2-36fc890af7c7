{"files.associations": {"*.dbclient-js": "javascript", "bitset": "cpp", "functional": "cpp", "fstream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "tuple": "cpp", "typeinfo": "cpp", "memory": "cpp", "memory_resource": "cpp", "shared_mutex": "cpp", "qvariantlist": "cpp", "regex": "cpp", "qmessagebox": "cpp", "string": "cpp", "*.inc": "cpp", "future": "cpp", "istream": "cpp", "ostream": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "iterator": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "type_traits": "cpp", "utility": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "streambuf": "cpp", "thread": "cpp", "variant": "cpp", "qstandardpaths": "cpp", "codecvt": "cpp", "forward_list": "cpp", "iomanip": "cpp", "*.impl": "cpp", "xstring": "cpp"}}