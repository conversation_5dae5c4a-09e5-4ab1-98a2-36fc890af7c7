
/**
 * @file userlogindialog.cpp
 * @brief 用户登录对话框实现文件
 *
 * 实现用户登录界面交互、登录验证、失败次数统计、快捷键处理等。
 * 支持多平台，界面基于Qt Designer生成。
 */

#include "userlogindialog.h"
#include "appsettings.h"
#include "bussiness_interfaces_caller.h"
#include "checkresponse.h"
#include "messagebox.h"
#include "ui_userlogindialog.h"
// #include "userlogappender.h"
#include <QKeyEvent>

/**
 * @brief 构造函数
 * @param gl 全局状态指针
 * @param appname 应用名称
 * @param parent 父窗口指针
 */
UserLoginDialog::UserLoginDialog(UIGlobalState* gl, const QString& appname, QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::UserLoginDialog)
    , global(gl)
    , m_appname(appname)
{
    ui->setupUi(this);
}

/**
 * @brief 析构函数
 */
UserLoginDialog::~UserLoginDialog()
{
    delete ui;
}

/**
 * @brief 登录按钮点击槽函数，处理登录逻辑
 *
 * 1. 校验用户名和密码非空；
 * 2. 构造登录请求，调用后端接口；
 * 3. 登录失败计数，3次失败自动退出（非WASM平台）；
 * 4. 登录成功后保存Token、用户名、状态等。
 * 5. 支持多语言、界面交互、异步回调。
 */
void UserLoginDialog::on_pushButtonLogin_clicked()
{
    const QString username = ui->lineEditUserName->text();
    QString pwd = ui->lineEditPassword->text();

    if (username.isEmpty()) {
        messageBoxEx(QObject::tr("User name could not be empty!"), true);
        return;
    }
    if (pwd.isEmpty()) {
        messageBoxEx(QObject::tr("Password could not be empty!"), true);
        return;
    }

    // 如需加密传输密码，可启用MD5加密
    // pwd = createPWDMD5(pwd);

    setEnabled(false); // 禁用界面，防止重复点击
    ::GlobalStructs::LoginRequest req;
    req.set_username(username.toStdString());
    req.set_password(pwd.toStdString());

    auto callback_func = [=](WSRPC::ErrorCode code, const QString& errorstring, const ::GlobalStructs::LoginResponse& ret) {
        Q_ASSERT(global);
        this->setEnabled(true);
        CHECK_RPC_ERROR(global, code, errorstring);
        if (ret.respcode().code() != 0) {
            QString errorMsg = QString::fromStdString(ret.respcode().message());
            if(!errorMsg.isEmpty()) {
                messageBoxEx(errorMsg, true);
            }
            m_failedCount++;
            if (m_failedCount >= 3) {
#ifndef Q_OS_WASM
                messageBoxEx(QObject::tr("Login failed 3 times, please try again later!"), true);
                QApplication::quit();
#endif
            }
        }
        CHECK_RESPONSE(global, ret.respcode());

        m_failedCount = 0;
        global->setCurrentUserToken(QString::fromStdString(ret.token()));
        global->setCurrentUser(username);
        emit global->showTooltip(UIGlobalState::TooltipLevel::Normal, tr("Login success!"));

        global->setLoginState(true);

        // 可选：记录登录日志
        // saveUserLog(global->currentUserToken(), tr("User %1 login successfully!").arg(username),
        //     m_appname, 0, 0, AppSettings::instance().currentLanguage());
    };

    Global::call_login(req, callback_func, nullptr, DEFAULT_TIMEOUT_INTERVAL, AppSettings::instance().currentLanguageInfo().alias, global->clientName());
}

/**
 * @brief 事件过滤，处理语言切换和窗口显示
 * @param event 事件指针
 * @return 是否处理
 */
bool UserLoginDialog::event(QEvent* event)
{
    if (event->type() == QEvent::LanguageChange) {
        ui->retranslateUi(this);
    } else if (event->type() == QEvent::Show) {
        ui->retranslateUi(this);
        ui->lineEditUserName->setFocus();
        ui->lineEditUserName->clear();
        ui->lineEditPassword->clear();
    }

    return QWidget::event(event);
}

/**
 * @brief 键盘按下事件，支持回车快捷登录
 * @param event 键盘事件指针
 */
void UserLoginDialog::keyPressEvent(QKeyEvent* event)
{
    if (event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter) {
        on_pushButtonLogin_clicked();
    }
    return QWidget::keyPressEvent(event);
}
