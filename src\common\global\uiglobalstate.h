/**
 * @file uiglobalstate.h
 * @brief 界面全局状态管理类声明，负责管理客户端界面相关的全局信息、事件与信号
 */

#ifndef UIGLOBALSTATE_H
#define UIGLOBALSTATE_H

// #include "global_structs.h"
#include "global_structs.pb.h"   // 站点、用户等结构体定义
#include <QObject>

/**
 * @brief 界面全局状态管理类，负责管理客户端界面相关的全局信息、事件与信号
 *        包括用户、工程、主题、连接状态、站点信息等，提供信号通知机制
 */
class UIGlobalState : public QObject {
    Q_OBJECT
public:
    /**
     * @brief 主页提示级别枚举
     */
    enum TooltipLevel {
        Normal,  ///< 普通提示
        Warning, ///< 警告提示
        Error,   ///< 错误提示
    };

    /**
     * @brief 界面事件类型枚举
     */
    enum UIEvent {
        ConnectStateChanged,           ///< 连接状态改变
        CurrentUserChanged,            ///< 当前用户改变
        CurrentUserTokenChanged,       ///< 当前用户Token改变
        CurrentProjectChanged,         ///< 当前工程改变
        CurrentProjectIDChanged,       ///< 当前工程ID改变
        CurrentThemeNameChanged,       ///< 当前风格样式改变
        LoginStateChanged,             ///< 登录状态改变
        OpenProjectListPage,           ///< 打开工程列表页面
        ToolBarVisibleStateChanged,    ///< 工具栏可视化状态改变
        StationDatasReady,             ///< 站点数据准备完毕
        PlatformServerConnectStateChanged, ///< 平台服务器连接状态改变
        CHILD_EVENT_BASE,              ///< 扩展事件基础ID
    };

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit UIGlobalState(QObject* parent = nullptr);

public:
    /**
     * @brief 获取是否服务端已连接
     * @return 是否服务端已连接
     */

    /**
     * @brief 设置/获取服务端连接状态
     * @param newIsConnected 新的连接状态
     * @return 是否服务端已连接
     */
    bool isConnected() const;
    void setIsConnected(bool newIsConnected);

    /**
     * @brief 设置/获取当前用户
     * @param newCurrentUser 新的用户名
     * @return 当前用户
     */
    QString currentUser() const;
    void setCurrentUser(const QString& newCurrentUser);

    /**
     * @brief 获取当前工程名
     * @return 当前工程名
     */
    QString currentProject() const;

    /**
     * @brief 设置/获取当前工程ID及名称
     * @param newCurrentProjectID 新工程ID
     * @param newCurrentProject 新工程名
     * @return 当前工程ID
     */
    int currentProjectID() const;
    void setCurrentProject(int newCurrentProjectID, const QString& newCurrentProject);

    /**
     * @brief 设置/获取当前主题样式名称
     * @param newCurrentThemeName 新主题名
     * @return 当前样式名称
     */
    QString currentThemeName() const;
    void setCurrentThemeName(const QString& newCurrentThemeName);

    /**
     * @brief 设置/获取登录状态
     * @param newloginState 新登录状态
     * @return 登录状态
     */
    bool loginState() const;
    void setLoginState(bool newloginState);

    /**
     * @brief 设置/获取当前站点信息
     * @param newCurrentStations 新站点信息
     * @return 当前站点信息
     */
    const GlobalStructs::SiteListResponse& currentStations() const;
    void setCurrentStations(const GlobalStructs::SiteListResponse& newCurrentStations);

    /**
     * @brief 设置/获取当前用户Token
     * @param newCurrentUserToken 新Token
     * @return 当前用户Token
     */
    QString currentUserToken() const;
    void setCurrentUserToken(const QString& newCurrentUserToken);

    /**
     * @brief 设置/获取平台服务器连接状态
     * @param connected 新的连接状态
     * @return 平台服务器是否已连接
     */
    void setPlatformServerConnected(bool connected);
    bool isPlatformServerConnected() const;

    /**
     * @brief 设置/获取工具栏可见性
     * @param newIsToolBarVisible 新的可见性
     * @return 工具栏是否可见
     */
    bool isToolBarVisible() const;
    void setIsToolBarVisible(bool newIsToolBarVisible);

    /**
     * @brief 设置/获取客户端名称
     * @param newClientName 新客户端名
     * @return 客户端名称
     */
    QString clientName() const;
    void setClientName(const QString &newClientName);


signals:
    /**
     * @brief UI事件通知信号
     * @param uievent ui事件号（UIEvent枚举）
     * @param params 附加参数
     */
    void uiEventNotify(int uievent, const QVariantList& params);

    /**
     * @brief 显示提示信号
     * @param level 提示级别
     * @param msg 消息内容
     */
    void showTooltip(TooltipLevel level, const QString& msg);

    /**
     * @brief 显示用户日志信号
     */
    void showUserLog();

private:
    bool m_isConnected = false; ///< 服务器是否已连接
    QString m_currentUser = {}; ///< 当前用户
    QString m_currentUserToken = {}; ///< 当前用户Token
    QString m_currentProject = {}; ///< 当前工程
    QString m_currentThemeName = {}; ///< 当前风格名称
    bool m_loginState = false; ///< 登录状态
    int m_currentProjectID = 0; ///< 当前工程ID
    GlobalStructs::SiteListResponse m_currentStations; ///< 当前站点信息
    bool m_platformServerConnected = false; ///< 平台服务器连接状态
    bool m_isToolBarVisible = true; ///< 工具栏可见性
    QString m_clientName; ///< 客户端名称
};
#endif // UIGLOBALSTATE_H
