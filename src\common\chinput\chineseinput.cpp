
// chineseinput.cpp
// 实现 WASMChineseInput 类，支持 WebAssembly 平台下的中文输入弹窗与辅助

#include "chineseinput.h"
#include <QDebug>
#ifdef Q_OS_WASM
#include <emscripten.h>
#include <emscripten/html5.h>

// 获取用户输入，因为Qt不支持直接输入中文。。
EM_JS(const char*, getInput, (const char* str), {
    // 1. 将C字符串转为JS字符串
    const text = UTF8ToString(str);
    // 2. 弹出浏览器输入框
    val = prompt("信息输入", text);
    if (val == null) {
        val = text;
    }
    var jstring = val;
    // 3. 分配内存并写回C字符串
    var lengthBytes = lengthBytesUTF8(jstring) + 1;
    var stringOnWasmHeap = _malloc(lengthBytes);
    stringToUTF8(jstring, stringOnWasmHeap, lengthBytes);
    return stringOnWasmHeap;
})
#endif


// 构造函数，初始化 WASMChineseInput 对象
WASMChineseInput::WASMChineseInput(QObject* parent)
    : QObject { parent }
{
    // 可选：注入全局JS函数
    // injectJs();
    // 可选：注册控件失焦回调
    // auto ret = emscripten_set_blur_callback(EMSCRIPTEN_EVENT_TARGET_WINDOW, this, 1, focusevent_callback);
}

/**
 * @brief 获取用户输入（WASM平台弹窗，桌面直接返回原文）
 * @param currentText 当前文本
 * @return 用户输入结果
 */
QString WASMChineseInput::getUserInput(const QString& currentText)
{
#ifdef Q_OS_WASM
    QString input = "";
    input = getInput(currentText.toUtf8().data());
    // 可选：getInputX(currentText.toUtf8().data());
    // 可选：input = getText();
    return input;
#else
    return currentText;
#endif
}
