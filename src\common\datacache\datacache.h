
#ifndef datacache_h_
#define datacache_h_

#include <QtCore>

/**
 * @brief 线程安全的数据缓存单例类，支持全局任意类型数据的临时存取
 *        适用于跨模块、跨线程的临时数据共享
 */
class DataCache {
public:
    /**
     * @brief 获取 DataCache 单例对象
     * @return 单例引用
     */
    static DataCache& instance()
    {
        static DataCache _instance;
        return _instance;
    }

    /**
     * @brief 获取指定 key 的缓存值（线程安全）
     * @param key 缓存键
     * @return 缓存值 QVariant
     */
    QVariant cacheValue(const QString& key)
    {
        QMutexLocker locker(&m_dataCacheMutex);
        return m_dataCache[key];
    }

    /**
     * @brief 设置指定 key 的缓存值（线程安全）
     * @param key 缓存键
     * @param var 缓存值
     */
    void setCacheValue(const QString& key, const QVariant& var)
    {
        if (key.isEmpty())
            return;
        QMutexLocker locker(&m_dataCacheMutex);
        qDebug() << __FUNCTION__ << key << var;
        m_dataCache[key] = var;
    }

private:
    /**
     * @brief 构造函数（私有，单例模式）
     */
    DataCache()
    {
    }
    Q_DISABLE_COPY(DataCache) ///< 禁用拷贝构造和赋值

    QMap<QString, QVariant> m_dataCache; ///< 缓存数据表
    QMutex m_dataCacheMutex;             ///< 互斥锁，保证线程安全
};

#endif
