
/**
 * @file projectspage2.h
 * @brief 项目管理主页面（新版）声明文件
 *
 * 提供项目列表、最近打开、项目信息展示、刷新、双击进入等功能，支持多平台Qt桌面应用。
 * 支持项目筛选、数据模型、全局状态、信号槽等，便于团队协作和维护。
 */

#ifndef PROJECTSPAGE2_H
#define PROJECTSPAGE2_H

#include <QtWidgets>
// #include "global_structs.h"
#include "global_structs.pb.h"

namespace Ui {
class ProjectsPage2;
}

class ProjectInfoWidgt;
class UIGlobalState;

/**
 * @brief 项目管理主页面类（新版）
 *
 * 负责展示和管理所有项目，支持项目筛选、最近打开、刷新、项目信息展示、双击进入等。
 * 支持与全局状态、数据模型、信号槽联动，提升用户体验。
 */
class ProjectsPage2 : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param gl 全局状态指针
     * @param parent 父窗口指针
     */
    explicit ProjectsPage2(UIGlobalState* gl, QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ProjectsPage2();

protected:
    /**
     * @brief 筛选项目（全部/最近打开）
     * @param isRecentOpen 是否仅显示最近打开
     */
    void filterProjects(bool isRecentOpen);

    /**
     * @brief 向列表添加项目数据
     * @param data 项目信息结构体
     */
    void addProjectToList(const GlobalStructs::ProjectInfo& data);

    /**
     * @brief 界面显示事件，自动刷新项目列表
     * @param event 显示事件
     */
    void showEvent(QShowEvent* event) override;

    /**
     * @brief 事件过滤，处理多语言等
     * @param event 事件指针
     * @return 是否处理
     */
    bool event(QEvent* event) override;

    /**
     * @brief 刷新当前项目信息面板
     */
    void refreshCurrentProjectInfo();

private slots:
    /**
     * @brief “全部项目”按钮点击槽
     */
    void on_toolButtonProjectList_clicked();

    /**
     * @brief “最近打开”按钮点击槽
     */
    void on_toolButtonRecentlyOpen_clicked();

    /**
     * @brief “刷新”按钮点击槽
     */
    void on_pushButtonRefresh_clicked();

    /**
     * @brief 项目列表双击槽，进入项目
     * @param index 被双击的索引
     */
    void on_listView_doubleClicked(const QModelIndex& index);

    /**
     * @brief “返回”按钮点击槽
     * @param checked 按钮状态
     */
    void on_pushButtonReturn_clicked(bool checked);

    /**
     * @brief 刷新项目列表
     * @param notice 是否弹出提示
     */
    void refreshProjectList(bool notice);

    /**
     * @brief 项目选择变化槽
     * @param 当前索引
     * @param 上一个索引
     */
    void selectProjectChanged(const QModelIndex &, const QModelIndex &);

    void on_pushButtonLogout_clicked(bool checked);

private:
    Ui::ProjectsPage2* ui; ///< UI指针
    QSplitter m_splitter; ///< 主分割控件
    ProjectInfoWidgt* m_projectInfo = nullptr; ///< 项目信息面板
    UIGlobalState* m_global = nullptr; ///< 全局状态指针
    QStandardItemModel* m_model = nullptr; ///< 项目列表数据模型
    QMap<int, GlobalStructs::ProjectInfo> m_projectInfos; ///< 工程信息映射表
};

#endif // PROJECTSPAGE2_H
