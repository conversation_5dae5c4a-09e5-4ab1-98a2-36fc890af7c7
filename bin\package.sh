#!/bin/bash
set -x
VERSION="${VERSION}.$(git rev-list --count HEAD)"
echo FULL_VERSION: $VERSION
APP_NAME=$1

if [ -z $APP_NAME ]; then
    echo "Please input app name"
    exit 1
fi
target_path=/usr/local/${APP_NAME}-client
temp_target_path=./${APP_NAME}${target_path}
lib_target_path=${temp_target_path}/lib
bin_target_path=${temp_target_path}/bin
qtlib_path=/home/<USER>/6.6.3/gcc_64/lib
target_desktop_path=/usr/share/applications/${APP_NAME}.desktop
mkdir -p ${lib_target_path}
mkdir -p ${bin_target_path}
mkdir -p ./${APP_NAME}/usr/bin
mkdir -p ./${APP_NAME}/usr/share/applications
DEBIAN_PATH=./${APP_NAME}/DEBIAN
mkdir -p ${DEBIAN_PATH}



cp -r /home/<USER>/6.6.3/gcc_64/plugins/platforms ${temp_target_path}/platforms

mv ./${APP_NAME}/${APP_NAME} ${bin_target_path}/${APP_NAME}
mv ./${APP_NAME}/configs.ini ${bin_target_path}/configs.ini
mv ./${APP_NAME}/log4qt.properties ${bin_target_path}/log4qt.properties


cp -r ${qtlib_path}/libQt6Core.so.6.6.3 ${lib_target_path}/libQt6Core.so.6
cp -r ${qtlib_path}/libQt6Network.so.6.6.3 ${lib_target_path}/libQt6Network.so.6
cp -r ${qtlib_path}/libQt6WebSockets.so.6.6.3 ${lib_target_path}/libQt6WebSockets.so.6
cp -r ${qtlib_path}/libQt6Widgets.so.6.6.3 ${lib_target_path}/libQt6Widgets.so.6
cp -r ${qtlib_path}/libQt6Gui.so.6.6.3 ${lib_target_path}/libQt6Gui.so.6
cp -r ${qtlib_path}/libQt6PrintSupport.so.6.6.3 ${lib_target_path}/libQt6PrintSupport.so.6
cp -r ${qtlib_path}/libQt6DBus.so.6.6.3 ${lib_target_path}/libQt6DBus.so.6
cp -r ${qtlib_path}/libQt6XcbQpa.so.6.6.3 ${lib_target_path}/libQt6XcbQpa.so.6
cp -r ${qtlib_path}/libQt6OpenGL.so.6.6.3 ${lib_target_path}/libQt6OpenGL.so.6
cp -r ${qtlib_path}/libicui18n.so.56.1 ${lib_target_path}/libicui18n.so.56
cp -r ${qtlib_path}/libicuuc.so.56.1 ${lib_target_path}/libicuuc.so.56
cp -r ${qtlib_path}/libicudata.so.56.1 ${lib_target_path}/libicudata.so.56

cp -r ../Log4Qt/bin/liblog4qt.so.1.0.0 ${lib_target_path}/liblog4qt.so.1
# 拷贝配置文件
cp -r ./control ${DEBIAN_PATH}/control
# 拷贝 preinst postinst prerm postrm
cp -r ./postinst ${DEBIAN_PATH}/postinst
chmod +x ${DEBIAN_PATH}/postinst
cp -r ./postrm ${DEBIAN_PATH}/postrm
chmod +x ${DEBIAN_PATH}/postrm
# cp -r ./prerm ${DEBIAN_PATH}/prerm
# chmod +x ${DEBIAN_PATH}/prerm
# cp -r ./preinst ${DEBIAN_PATH}/preinst
# chmod +x ${DEBIAN_PATH}/preinst
# 拷贝exec.sh
cp -r ./exec.sh ./${APP_NAME}/usr/bin/${APP_NAME}
chmod +x ./${APP_NAME}/usr/bin/${APP_NAME}
# 拷贝desktop文件
cp -r ./temp.desktop ./${APP_NAME}${target_desktop_path}
# 拷贝icon文件
cp -r ./${APP_NAME}-icon.png ./${temp_target_path}/icon.png
# 删除dbconection.ini
rm -rf ./${APP_NAME}/*.*
# 查看目录大小
SIZE=$(du -s ./$APP_NAME | awk '{print $1}')
# 修改control文件
if [ $(uname -m) == "x86_64" ]; then
    ARCH=amd64 
else
    ARCH=$(uname -m)
fi
sed -i "s/ARCH/${ARCH}/g" ${DEBIAN_PATH}/control
sed -i "s/APP_NAME/${APP_NAME}/g" ${DEBIAN_PATH}/control
sed -i "s/package_size/${SIZE}/g" ${DEBIAN_PATH}/control
sed -i "s/package_version/${VERSION}/g" ${DEBIAN_PATH}/control
# 修改exec.sh文件
sed -i "s#target_path#${target_path}#g" ./${APP_NAME}/usr/bin/${APP_NAME}
sed -i "s/APP_NAME/${APP_NAME}/g" ./${APP_NAME}/usr/bin/${APP_NAME}
# 修改desktop文件
sed -i "s/package_version/${VERSION}/g" ./${APP_NAME}${target_desktop_path}
sed -i "s/APP_NAME/${APP_NAME}/g" ./${APP_NAME}${target_desktop_path}
sed -i "s#target_path#${target_path}#g" ./${APP_NAME}${target_desktop_path}
# 修改postinst文件
sed -i "s/APP_NAME/${APP_NAME}/g" ${DEBIAN_PATH}/postinst
sed -i "s#target_desktop_path#${target_desktop_path}#g" ${DEBIAN_PATH}/postinst
# 修改postrm文件
sed -i "s/APP_NAME/${APP_NAME}/g" ${DEBIAN_PATH}/postrm
# 修改preinst文件
# sed -i "s/APP_NAME/${APP_NAME}/g" ${DEBIAN_PATH}/preinst

dpkg-deb -b $APP_NAME $APP_NAME-client-kylin-v10.1-sp1-${ARCH}.deb
cp ./$APP_NAME-client-kylin-v10.1-sp1-${ARCH}.deb ../$APP_NAME-client-kylin-v10.1-sp1-${ARCH}.deb
