#include "netinfos.h"
#include "optionsdialog.h"
#include "uiglobalstate.h"

#define NETCARD_FILE_CONFIG_NAME "netcard.ini"

#define KEY_GROUP_NAME "Config"
#define KEY_NAME "NetCard"

QString NetInfos::currentNetCardName()
{
    const QString inifile = QCoreApplication::applicationDirPath() + "/" + NETCARD_FILE_CONFIG_NAME;

    QString name;
    QSettings settings(inifile, QSettings::IniFormat);
    settings.beginGroup(KEY_GROUP_NAME);

    name = settings.value(KEY_NAME).toString();

    settings.endGroup();

    return name;
}

void NetInfos::writeCurrentNetCardName(const QString& cardname)
{
    const QString inifile = QCoreApplication::applicationDirPath() + "/" + NETCARD_FILE_CONFIG_NAME;

    QString name;
    QSettings settings(inifile, QSettings::IniFormat);
    settings.beginGroup(KEY_GROUP_NAME);

    settings.setValue(KEY_NAME, cardname);

    settings.endGroup();
}

void NetInfos::showNetCardSetDialog(UIGlobalState* global)
{
    OptionsDialog::execFormType(global, OptionsDialog::PageTypeNetcard);
}
