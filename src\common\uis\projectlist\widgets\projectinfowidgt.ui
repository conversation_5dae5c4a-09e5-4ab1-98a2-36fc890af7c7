<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProjectInfoWidgt</class>
 <widget class="QWidget" name="ProjectInfoWidgt">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>559</width>
    <height>396</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="mainWidget" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget" native="true">
        <layout class="QGridLayout" name="gridLayout">
         <property name="leftMargin">
          <number>9</number>
         </property>
         <item row="0" column="1" colspan="2">
          <widget class="QLabel" name="projecteName">
           <property name="text">
            <string notr="true"/>
           </property>
          </widget>
         </item>
         <item row="1" column="1" colspan="2">
          <widget class="QLabel" name="projectePath">
           <property name="text">
            <string notr="true"/>
           </property>
          </widget>
         </item>
         <item row="0" column="0" rowspan="2">
          <widget class="QToolButton" name="toolButton">
           <property name="text">
            <string>...</string>
           </property>
           <property name="icon">
            <iconset resource="../../../../themes/themes.qrc">
             <normaloff>:/styles/default/images/project.png</normaloff>:/styles/default/images/project.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_2" native="true">
        <layout class="QFormLayout" name="formLayout">
         <item row="3" column="0">
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>Recent Opened:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="5" column="0">
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>Station Number:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLabel" name="labelRecentOpen">
           <property name="text">
            <string notr="true">TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="5" column="1">
          <widget class="QLabel" name="labelStationNumber">
           <property name="text">
            <string notr="true">TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="labelCreateDate">
           <property name="text">
            <string notr="true">TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="label">
           <property name="text">
            <string>Create Date:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>Project ID:</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignRight|Qt::AlignmentFlag::AlignTrailing|Qt::AlignmentFlag::AlignVCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="labelProjectID">
           <property name="text">
            <string notr="true">TextLabel</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_3" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>159</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="open">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string>Open</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_4" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../../themes/themes.qrc"/>
 </resources>
 <connections/>
</ui>
