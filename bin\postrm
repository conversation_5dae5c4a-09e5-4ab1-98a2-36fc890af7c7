#!/bin/bash

rm_to_desktop() {
    USERNAME=$1
    USER_HOME=$(eval echo ~$USERNAME)
    DESKTOP_DIR="$USER_HOME/Desktop"
    DESKTOP_FILE="$DESKTOP_DIR/APP_NAME.desktop"
    if [ -f "$DESKTOP_FILE" ]; then
        rm "$DESKTOP_FILE"
    fi
    DESKTOP_DIR="$USER_HOME/桌面"
    DESKTOP_FILE="$DESKTOP_DIR/APP_NAME.desktop"
    if [ -f "$DESKTOP_FILE" ]; then
        rm "$DESKTOP_FILE"
    fi
}

for USER in $(ls /home); do
    rm_to_desktop $USER
done