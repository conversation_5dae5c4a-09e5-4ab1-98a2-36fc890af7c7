#ifndef _path_h_
#define _path_h_

#include <QString>
#ifdef Q_OS_WIN
#include <QRegularExpression>
#endif


/**
     * @brief 判断是否是绝对路径
     * @param 路径
     * @return 是否是绝对路径
     */
inline bool isAbsolutePath(const QString& path)
{
#ifdef Q_OS_WIN
    // Windows 绝对路径检查
    // 匹配 C:\ 或 \\ 开头的路径
    QRegularExpression re(R"(^([a-zA-Z]:[\\\/]|[\\\/]{2}))");
    return re.match(path).hasMatch();
#else \
    // Linux 绝对路径检查
    return path.startsWith('/');
#endif
}

/**
     * @brief 连接路径
     * @param directory 目录
     * @param filename 文件名
     * @return 完整路径
     */
inline QString joinPath(const QString& directory, const QString& filename)
{
    if (!directory.endsWith("/")) {
        return directory + "/" + filename;
    } else {
        return directory + filename;
    }
}
#endif
