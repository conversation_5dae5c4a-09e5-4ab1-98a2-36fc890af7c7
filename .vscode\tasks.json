{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Clean(PertPlugins)",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/src/server/src/pertlibs/build"
            ],
            "options": {
                "cwd": "${workspaceFolder}/src/server/src"
            },
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(PertPlugins)-Debug",
            "type": "shell",
            "command": "/opt/Qt/5.15.2/gcc_64/bin/qmake",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/server/src/pertlibs/build/Makefile",
                "${workspaceFolder}/src/server/src/pertlibs.pro"
            ],
            "group": {
                "kind": "build"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(PertPlugins)",
            "dependsOn": [
                "QMake(PertPlugins)-Debug"
            ],
            "type": "shell",
            "command": "make -j32",
            "args": [],
            "group": {
                "kind": "build"
            },
            "options": {
                "cwd": "${workspaceFolder}/src/server/src/pertlibs/build" // 指定make的工作目录
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Rebuild(PertPlugin)",
            "dependsOn": [
                "Clean(PertPlugins)",
                "Build(PertPlugins)"
            ],
            "group": {
                "kind": "build"
            }
        },
        {
            "label": "Clean(4in1server)",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/src/server/build/"
            ],
            "options": {
                "cwd": "${workspaceFolder}/src/server/build/"
            },
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(4in1server)-Debug",
            "type": "shell",
            "command": "/opt/Qt/5.15.2/gcc_64/bin/qmake",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/server/build/Makefile",
                "${workspaceFolder}/src/server/server.pro"
            ],
            "group": {
                "kind": "build"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(4in1server)",
            "dependsOn": [
                "QMake(4in1server)-Debug"
            ],
            "type": "shell",
            "command": "bash ${workspaceFolder}/src/server/build.sh",
            "args": [],
            "group": {
                "kind": "build"
            },
            "options": {
                "cwd": "${workspaceFolder}/src/server/build",
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Rebuild(4in1server)",
            "dependsOn": [
                "Clean(4in1server)",
                "Build(4in1server)"
            ],
            "group": {
                "kind": "build"
            }
        },
        {
            "label": "Clean(FEDIS)",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/src/fedis/build/"
            ],
            "options": {
                "cwd": "${workspaceFolder}/src/fedis/build/"
            },
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(FEDIS)-Debug",
            "type": "shell",
            "command": "/opt/Qt/5.15.2/gcc_64/bin/qmake",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/fedis/build/Makefile",
                "${workspaceFolder}/src/fedis/fedis.pro"
            ],
            "group": {
                "kind": "build"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(FEDIS)",
            "dependsOn": [
                "QMake(FEDIS)-Debug"
            ],
            "type": "shell",
            "command": "make -j32",
            "args": [],
            "group": {
                "kind": "build"
            },
            "options": {
                "cwd": "${workspaceFolder}/src/fedis/build",
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Rebuild(FEDIS)",
            "dependsOn": [
                "Clean(FEDIS)",
                "Build(FEDIS)"
            ],
            "group": {
                "kind": "build"
            }
        },
        {
            "label": "Clean(REST)",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "${workspaceFolder}/src/rest/build/"
            ],
            "options": {
                "cwd": "${workspaceFolder}/src/rest/build/"
            },
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(REST)-Debug",
            "type": "shell",
            "command": "/opt/Qt/5.15.2/gcc_64/bin/qmake",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/rest/build/Makefile",
                "${workspaceFolder}/src/rest/rest.pro"
            ],
            "group": {
                "kind": "build"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(REST)",
            "dependsOn": [
                "QMake(REST)-Debug"
            ],
            "type": "shell",
            "command": "make -j32",
            "args": [],
            "group": {
                "kind": "build"
            },
            "options": {
                "cwd": "${workspaceFolder}/src/rest/build",
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Rebuild(REST)",
            "dependsOn": [
                "Clean(REST)",
                "Build(REST)"
            ],
            "group": {
                "kind": "build"
            }
        },
        {
            "label": "QMake(PertPlugins)-Debug (Win32)",
            "type": "shell",
            "command": "C:/Qt/5.15.2/msvc2019/bin/qmake.exe",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/server/src/pertlibs/build/Makefile",
                "${workspaceFolder}/src/server/src/pertlibs.pro"
            ],
            "group": { "kind": "build" },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(PertPlugins) (Win32)",
            "dependsOn": ["QMake(PertPlugins)-Debug (Win32)"],
            "type": "shell",
            "command": "cmd",
            "args": [
                "/c",
                "call \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Auxiliary\\Build\\vcvars32.bat\" && C:\\Qt\\Tools\\QtCreator\\bin\\jom\\jom.exe -j 32"
            ],
            "group": { "kind": "build" },
            "options": {
                "cwd": "${workspaceFolder}/src/server/src/pertlibs/build",
                "shell": { "executable": "cmd.exe" }
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Clean(PertPlugins) (Win32)",
            "type": "shell",
            "command": "cmd",
            "args": ["/c", "rmdir /s /q \"${workspaceFolder}/src/server/src/pertlibs/build\""] ,
            "options": { "cwd": "${workspaceFolder}/src/server/src" },
            "group": { "kind": "build", "isDefault": false },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(4in1server)-Debug (Win32)",
            "type": "shell",
            "command": "C:/Qt/5.15.2/msvc2019/bin/qmake.exe",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/server/build/Makefile",
                "${workspaceFolder}/src/server/server.pro"
            ],
            "group": { "kind": "build" },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(4in1server) (Win32)",
            "dependsOn": ["QMake(4in1server)-Debug (Win32)"],
            "type": "shell",
            "command": "cmd",
            "args": [
                "/c",
                "call \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Auxiliary\\Build\\vcvars32.bat\" && C:\\Qt\\Tools\\QtCreator\\bin\\jom\\jom.exe -j 32"
            ],
            "group": { "kind": "build" },
            "options": {
                "cwd": "${workspaceFolder}/src/server/build",
                "shell": { "executable": "cmd.exe" }
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Clean(4in1server) (Win32)",
            "type": "shell",
            "command": "cmd",
            "args": ["/c", "rmdir /s /q \"${workspaceFolder}/src/server/build\""] ,
            "options": { "cwd": "${workspaceFolder}/src/server" },
            "group": { "kind": "build", "isDefault": false },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(FEDIS)-Debug (Win32)",
            "type": "shell",
            "command": "C:/Qt/5.15.2/msvc2019/bin/qmake.exe",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/fedis/build/Makefile",
                "${workspaceFolder}/src/fedis/fedis.pro"
            ],
            "group": { "kind": "build" },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(FEDIS) (Win32)",
            "dependsOn": ["QMake(FEDIS)-Debug (Win32)"],
            "type": "shell",
            "command": "cmd",
            "args": [
                "/c",
                "call \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Auxiliary\\Build\\vcvars32.bat\" && C:\\Qt\\Tools\\QtCreator\\bin\\jom\\jom.exe -j 32"
            ],
            "group": { "kind": "build" },
            "options": {
                "cwd": "${workspaceFolder}/src/fedis/build",
                "shell": { "executable": "cmd.exe" }
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Clean(FEDIS) (Win32)",
            "type": "shell",
            "command": "cmd",
            "args": ["/c", "rmdir /s /q \"${workspaceFolder}/src/fedis/build\""] ,
            "options": { "cwd": "${workspaceFolder}/src/fedis" },
            "group": { "kind": "build", "isDefault": false },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "QMake(REST)-Debug (Win32)",
            "type": "shell",
            "command": "C:/Qt/5.15.2/msvc2019/bin/qmake.exe",
            "args": [
                "CONFIG+=debug",
                "CONFIG+=c++17",
                "-o",
                "${workspaceFolder}/src/rest/build/Makefile",
                "${workspaceFolder}/src/rest/rest.pro"
            ],
            "group": { "kind": "build" },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Build(REST) (Win32)",
            "dependsOn": ["QMake(REST)-Debug (Win32)"],
            "type": "shell",
            "command": "cmd",
            "args": [
                "/c",
                "call \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Professional\\VC\\Auxiliary\\Build\\vcvars32.bat\" && C:\\Qt\\Tools\\QtCreator\\bin\\jom\\jom.exe -j 32"
            ],
            "group": { "kind": "build" },
            "options": {
                "cwd": "${workspaceFolder}/src/rest/build",
                "shell": { "executable": "cmd.exe" }
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "Clean(REST) (Win32)",
            "type": "shell",
            "command": "cmd",
            "args": ["/c", "rmdir /s /q \"${workspaceFolder}/src/rest/build\""] ,
            "options": { "cwd": "${workspaceFolder}/src/rest" },
            "group": { "kind": "build", "isDefault": false },
            "problemMatcher": "$msCompile"
        }
    ]
}