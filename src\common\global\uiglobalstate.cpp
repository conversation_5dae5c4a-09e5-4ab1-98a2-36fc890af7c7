
// uiglobalstate.cpp
// 实现 UIGlobalState 类，负责界面全局状态的管理与信号通知

#include "uiglobalstate.h"           // 头文件声明
#include "netinfos.h"                // 网卡信息管理
#include <QtCore>
#include "bussiness_interfaces_caller.h" // 业务接口调用

// 构造函数，初始化全局状态对象
UIGlobalState::UIGlobalState(QObject* parent)
    : QObject { parent }
{
}

// 获取服务端连接状态
bool UIGlobalState::isConnected() const
{
    return m_isConnected;
}

// 设置服务端连接状态，并发射事件信号
void UIGlobalState::setIsConnected(bool newIsConnected)
{
    if (newIsConnected != m_isConnected) {
        m_isConnected = newIsConnected;
        emit uiEventNotify(UIEvent::ConnectStateChanged, { newIsConnected });
    }
}

// 获取当前用户
QString UIGlobalState::currentUser() const
{
    return m_currentUser;
}

// 设置当前用户，并发射事件信号
void UIGlobalState::setCurrentUser(const QString& newCurrentUser)
{
    if (m_currentUser == newCurrentUser)
        return;
    m_currentUser = newCurrentUser;
    emit uiEventNotify(UIEvent::CurrentUserChanged, { newCurrentUser });
}

// 获取当前工程名
QString UIGlobalState::currentProject() const
{
    return m_currentProject;
}


// 设置当前工程ID和名称，切换工程时重置网卡并发射相关事件
void UIGlobalState::setCurrentProject(int newCurrentProjectID, const QString& newCurrentProject)
{
    m_currentProject = newCurrentProject;
    if (newCurrentProjectID != m_currentProjectID) {
        m_currentProjectID = newCurrentProjectID;
        emit uiEventNotify(UIEvent::CurrentProjectIDChanged, { newCurrentProjectID });

        // 20250619 要求切换工程后重置网卡
         NetInfos::writeCurrentNetCardName("");

        // 通知服务端切换工程
        GlobalStructs::NeedOpenProjectRequest req;
        req.set_token(m_currentUserToken.toStdString());
        req.set_projectname(newCurrentProject.toStdString());
        req.set_projectid(newCurrentProjectID);
        Global::call_openProject(req, nullptr, nullptr, DEFAULT_TIMEOUT_INTERVAL, "zh_cn", m_clientName);
    }
    // 工程名变化也发射事件
    emit uiEventNotify(UIEvent::CurrentProjectChanged, { newCurrentProject });
}

QString UIGlobalState::currentThemeName() const
{
    return m_currentThemeName;
}


// 设置当前主题样式名称，并发射事件信号
void UIGlobalState::setCurrentThemeName(const QString& newCurrentThemeName)
{
    if (m_currentThemeName == newCurrentThemeName)
        return;
    m_currentThemeName = newCurrentThemeName;
    emit uiEventNotify(UIEvent::CurrentThemeNameChanged, { newCurrentThemeName });
}


// 获取登录状态
bool UIGlobalState::loginState() const
{
    return m_loginState;
}

// 设置登录状态，并发射事件信号
void UIGlobalState::setLoginState(bool newloginState)
{
    if (newloginState == m_loginState)
        return;
    m_loginState = newloginState;
    emit uiEventNotify(UIEvent::LoginStateChanged, { newloginState });
}


// 获取当前站点信息
const GlobalStructs::SiteListResponse& UIGlobalState::currentStations() const
{
    return m_currentStations;
}

// 设置当前站点信息，并发射事件信号
void UIGlobalState::setCurrentStations(const GlobalStructs::SiteListResponse& newCurrentStations)
{
    m_currentStations = newCurrentStations;
    emit uiEventNotify(UIEvent::StationDatasReady, {});
}


// 获取当前用户Token
QString UIGlobalState::currentUserToken() const
{
    return m_currentUserToken;
}

// 设置当前用户Token，并发射事件信号
void UIGlobalState::setCurrentUserToken(const QString& newCurrentUserToken)
{
    m_currentUserToken = newCurrentUserToken;
    emit uiEventNotify(UIEvent::CurrentUserTokenChanged, { newCurrentUserToken });
}


// 设置平台服务器连接状态，并发射事件信号
void UIGlobalState::setPlatformServerConnected(bool connected)
{
    m_platformServerConnected = connected;
    emit uiEventNotify(UIGlobalState::PlatformServerConnectStateChanged, { connected });
}

// 获取平台服务器连接状态
bool UIGlobalState::isPlatformServerConnected() const
{
    return m_platformServerConnected;
}


// 获取工具栏可见性
bool UIGlobalState::isToolBarVisible() const
{
    return m_isToolBarVisible;
}

// 设置工具栏可见性，并发射事件信号
void UIGlobalState::setIsToolBarVisible(bool newIsToolBarVisible)
{
    if (newIsToolBarVisible != m_isToolBarVisible) {
        m_isToolBarVisible = newIsToolBarVisible;
        emit uiEventNotify(UIGlobalState::ToolBarVisibleStateChanged, { newIsToolBarVisible });
    }
}


// 获取客户端名称
QString UIGlobalState::clientName() const
{
    return m_clientName;
}

// 设置客户端名称
void UIGlobalState::setClientName(const QString& newClientName)
{
    m_clientName = newClientName;
}


// 获取当前工程ID
int UIGlobalState::currentProjectID() const
{
    return m_currentProjectID;
}
