#!/bin/bash
# Code to copy the desktop shortcut goes here

DESKTOP_FILE=target_desktop_path

# Function to copy desktop file to a user's desktop
copy_to_desktop() {
    USERNAME=$1
    USER_HOME=$(eval echo ~$USERNAME)
    DESKTOP_DIR="$USER_HOME/Desktop"
    
    if [ -d "$DESKTOP_DIR" ]; then
        cp "$DESKTOP_FILE" "$DESKTOP_DIR/"
        chmod +x "$DESKTOP_DIR/APP_NAME.desktop"
    fi
    DESKTOP_DIR="$USER_HOME/桌面"
    if [ -d "$DESKTOP_DIR" ]; then
        cp "$DESKTOP_FILE" "$DESKTOP_DIR/"
        chmod +x "$DESKTOP_DIR/APP_NAME.desktop"
    fi
}

# Get a list of all users with a home directory in /home
for USER in $(ls /home); do
    copy_to_desktop $USER
done