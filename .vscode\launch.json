{"version": "0.2.0", "configurations": [{"name": "Server", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/src/server/bin/4in1_server", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/src/server/bin", "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "environment": [{"name": "QTDIR", "value": "/opt/Qt/5.15.2/gcc_64"}, {"name": "LD_LIBRARY_PATH", "value": "/opt/Qt/5.15.2/gcc_64/lib:/opt/Qt/5.15.2/gcc_64/plugins/imageformats:/usr/lib/x86_64-linux-gnu:${workspaceFolder}/src/server/bin:${userHome}/4in1_libs/x64/Debug/log4qt/lib:${userHome}/4in1_libs/x64/Debug/logprinterex/lib:${userHome}/4in1_libs/x64/Debug/qrequest/lib:${userHome}/4in1_libs/x64/Debug/qxlsx/lib:${userHome}/4in1_libs/x64/Debug/grpc/lib:${userHome}/4in1_libs/x64/Debug/wsrpc/lib"}, {"name": "PATH", "value": "${env:PATH}:${env:QTDIR}/bin"}]}, {"name": "FEDIS", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/fedis/fedis", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/bin/fedis", "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "environment": [{"name": "LD_LIBRARY_PATH", "value": "/opt/Qt/5.15.2/gcc_64/lib:/opt/Qt/5.15.2/gcc_64/plugins/imageformats:/usr/lib/x86_64-linux-gnu:${workspaceFolder}/src/common/east_lan/linux/x64:${workspaceFolder}/src/server/bin:${userHome}/4in1_libs/x64/Debug/log4qt/lib:${userHome}/4in1_libs/x64/Debug/logprinterex/lib:${userHome}/4in1_libs/x64/Debug/qrequest/lib:${userHome}/4in1_libs/x64/Debug/qxlsx/lib:${userHome}/4in1_libs/x64/Debug/grpc/lib:${userHome}/4in1_libs/x64/Debug/wsrpc/lib"}, {"name": "PATH", "value": "${env:PATH}:${env:QTDIR}/bin"}]}, {"name": "REST", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/rest/rest", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/bin/rest", "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "environment": [{"name": "QTDIR", "value": "/opt/Qt/5.15.2/gcc_64"}, {"name": "LD_LIBRARY_PATH", "value": "/opt/Qt/5.15.2/gcc_64/lib:/opt/Qt/5.15.2/gcc_64/plugins/imageformats:/usr/lib/x86_64-linux-gnu:${workspaceFolder}/src/common/east_lan/linux/x64:${workspaceFolder}/src/server/bin:${userHome}/4in1_libs/x64/Debug/log4qt/lib:${userHome}/4in1_libs/x64/Debug/logprinterex/lib:${userHome}/4in1_libs/x64/Debug/qrequest/lib:${userHome}/4in1_libs/x64/Debug/qxlsx/lib:${userHome}/4in1_libs/x64/Debug/grpc/lib:${userHome}/4in1_libs/x64/Debug/wsrpc/lib"}, {"name": "PATH", "value": "${env:PATH}:${env:QTDIR}/bin"}]}, {"name": "Server (Windows)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/src/server/bin/4in1_server.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/src/server/bin", "environment": [{"name": "QTDIR", "value": "C:/Qt/5.15.2/msvc2019"}, {"name": "PATH", "value": "${env:PATH};${workspaceFolder}/src/server/bin;C:/Qt/5.15.2/msvc2019/bin"}]}, {"name": "FEDIS (Windows)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/bin/fedis/fedis.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/bin/fedis", "environment": [{"name": "QTDIR", "value": "C:/Qt/5.15.2/msvc2019"}, {"name": "PATH", "value": "${env:PATH};${workspaceFolder}/bin/fedis;C:/Qt/5.15.2/msvc2019/bin"}]}, {"name": "REST (Windows)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/bin/rest/rest.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/bin/rest", "environment": [{"name": "QTDIR", "value": "C:/Qt/5.15.2/msvc2019"}, {"name": "PATH", "value": "${env:PATH};${workspaceFolder}/bin/rest;C:/Qt/5.15.2/msvc2019/bin"}]}]}