#ifndef PROJECTWIDGET_H
#define PROJECTWIDGET_H

#include <QWidget>
#include <QStyleOption>
#include <QPainter>


namespace Ui {
class ProjectWidget;
}

class ProjectWidget : public QWidget {
    Q_OBJECT

public:
    explicit ProjectWidget(QWidget* parent = nullptr);
    ~ProjectWidget();
    /**
     * @brief setProjectName 设置工程名称
     * @param name 工程名称
     */
    void setProjectName(const QString& name);
    /**
     * @brief setCreateTime 设置创建时间
     * @param time 创建时间
     */
    void setCreateTime(const QString& time);
    /**
     * @brief setChecked 设置选中状态
     * @param checked 选中状态
     */
    void setChecked(bool checked);

    void setHovered(bool hover);

protected:
    bool event(QEvent* event);
    // void paintEvent(QPaintEvent*)
    // {
    //     QStyleOption opt;
    //     opt.init(this);
    //     QPainter p(this);
    //     style()->drawPrimitive(QStyle::PE_Widget, &opt, &p, this);
    // }

private:
    Ui::ProjectWidget* ui;
    bool m_isChecked = false;
};

#endif // PROJECTWIDGET_H
