Sub CreateMonitorSyncModuleSequenceDiagram()
    Dim vsoPage As Visio.Page
    Dim vsoShape As Visio.Shape
    
    ' 创建新页面
    Set vsoPage = ActiveDocument.Pages.Add
    vsoPage.Name = "监视同步模块交互时序图"
    
    ' 设置页面大小
    vsoPage.PageSheet.CellsU("PageWidth").FormulaU = "24 in"
    vsoPage.PageSheet.CellsU("PageHeight").FormulaU = "16 in"
    
    ' 定义参与者位置和宽度（自适应文字长度）
    Dim actorX(8) As Double
    Dim actorWidth(8) As Double
    
    actorX(0) = 1.5: actorWidth(0) = 1.4    ' 客户端
    actorX(1) = 3.5: actorWidth(1) = 2.2    ' VariableMonitorThread
    actorX(2) = 6.5: actorWidth(2) = 2.0    ' FileSystemWatcher
    actorX(3) = 9: actorWidth(3) = 1.8      ' TestPluginManage
    actorX(4) = 11.5: actorWidth(4) = 2.2   ' MonitorPlatformStatus
    actorX(5) = 14.5: actorWidth(5) = 2.0   ' SynchronizeCondition
    actorX(6) = 17: actorWidth(6) = 1.8     ' ServiceManage
    actorX(7) = 19.5: actorWidth(7) = 2.0   ' XlsxDispatchThread
    actorX(8) = 22: actorWidth(8) = 1.8     ' 平台API
    
    ' 创建参与者（自适应宽度）
    Set vsoShape = vsoPage.DrawRectangle(actorX(0) - actorWidth(0) / 2, 14.5, actorX(0) + actorWidth(0) / 2, 15.5)
    vsoShape.Text = "客户端"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(1) - actorWidth(1) / 2, 14.5, actorX(1) + actorWidth(1) / 2, 15.5)
    vsoShape.Text = "VariableMonitorThread"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(2) - actorWidth(2) / 2, 14.5, actorX(2) + actorWidth(2) / 2, 15.5)
    vsoShape.Text = "FileSystemWatcher"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(3) - actorWidth(3) / 2, 14.5, actorX(3) + actorWidth(3) / 2, 15.5)
    vsoShape.Text = "TestPluginManage"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(4) - actorWidth(4) / 2, 14.5, actorX(4) + actorWidth(4) / 2, 15.5)
    vsoShape.Text = "MonitorPlatformStatus"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(5) - actorWidth(5) / 2, 14.5, actorX(5) + actorWidth(5) / 2, 15.5)
    vsoShape.Text = "SynchronizeCondition"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(6) - actorWidth(6) / 2, 14.5, actorX(6) + actorWidth(6) / 2, 15.5)
    vsoShape.Text = "ServiceManage"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(7) - actorWidth(7) / 2, 14.5, actorX(7) + actorWidth(7) / 2, 15.5)
    vsoShape.Text = "XlsxDispatchThread"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    Set vsoShape = vsoPage.DrawRectangle(actorX(8) - actorWidth(8) / 2, 14.5, actorX(8) + actorWidth(8) / 2, 15.5)
    vsoShape.Text = "平台API"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    
    ' 创建生命线
    Dim i As Integer
    For i = 0 To 8
        Set vsoShape = vsoPage.DrawLine(actorX(i), 14.5, actorX(i), 2)
        vsoShape.CellsU("LinePattern").FormulaU = "2"
        vsoShape.CellsU("LineColor").FormulaU = "RGB(100,100,100)"
    Next i
    
    ' 创建消息序列（避免重叠）
    Dim msgY As Double
    msgY = 14
    
    ' 1. 初始化监视模块
    CreateMessageWithText vsoPage, actorX(0), actorX(6), msgY, "1.初始化监视模块", 1.4
    msgY = msgY - 0.4
    
    ' 2. 创建变量监控线程
    CreateMessageWithText vsoPage, actorX(6), actorX(1), msgY, "2.创建变量监控线程", 1.6
    msgY = msgY - 0.4
    
    ' 3. 初始化文件系统监视器
    CreateMessageWithText vsoPage, actorX(6), actorX(2), msgY, "3.初始化文件系统监视器", 1.8
    msgY = msgY - 0.4
    
    ' 4. 添加监视路径
    CreateSelfMessageWithText vsoPage, actorX(2), msgY, "4.添加监视路径", 1.4
    msgY = msgY - 0.4
    
    ' 5. 加载测试插件
    CreateMessageWithText vsoPage, actorX(6), actorX(3), msgY, "5.加载测试插件", 1.4
    msgY = msgY - 0.4
    
    ' 6. 启动平台状态监控
    CreateMessageWithText vsoPage, actorX(6), actorX(4), msgY, "6.启动平台状态监控", 1.6
    msgY = msgY - 0.4
    
    ' 7. 启动定时器
    CreateSelfMessageWithText vsoPage, actorX(4), msgY, "7.启动定时器", 1.2
    msgY = msgY - 0.4
    
    ' 8. 开始变量监控
    CreateMessageWithText vsoPage, actorX(0), actorX(1), msgY, "8.开始变量监控", 1.4
    msgY = msgY - 0.4
    
    ' 9. 启动定时器监控
    CreateSelfMessageWithText vsoPage, actorX(1), msgY, "9.启动定时器监控", 1.4
    msgY = msgY - 0.4
    
    ' 10. 获取在线变量值
    CreateMessageWithText vsoPage, actorX(1), actorX(8), msgY, "10.获取在线变量值", 1.6
    msgY = msgY - 0.4
    
    ' 11. 返回变量数据
    CreateMessageWithText vsoPage, actorX(8), actorX(1), msgY, "11.返回变量数据", 1.4
    msgY = msgY - 0.4
    
    ' 12. 更新监控缓存
    CreateSelfMessageWithText vsoPage, actorX(1), msgY, "12.更新监控缓存", 1.4
    msgY = msgY - 0.4
    
    ' 13. 文件变化检测
    CreateSelfMessageWithText vsoPage, actorX(2), msgY, "13.文件变化检测", 1.4
    msgY = msgY - 0.4
    
    ' 14. 目录变化通知
    CreateMessageWithText vsoPage, actorX(2), actorX(6), msgY, "14.目录变化通知", 1.4
    msgY = msgY - 0.4
    
    ' 15. 触发Excel解析
    CreateMessageWithText vsoPage, actorX(6), actorX(7), msgY, "15.触发Excel解析", 1.4
    msgY = msgY - 0.4
    
    ' 16. 读取用例数据
    CreateSelfMessageWithText vsoPage, actorX(7), msgY, "16.读取用例数据", 1.4
    msgY = msgY - 0.4
    
    ' 17. 解析同步条件
    CreateMessageWithText vsoPage, actorX(7), actorX(5), msgY, "17.解析同步条件", 1.4
    msgY = msgY - 0.4
    
    ' 18. 加载XML配置
    CreateSelfMessageWithText vsoPage, actorX(5), msgY, "18.加载XML配置", 1.4
    msgY = msgY - 0.4
    
    ' 19. 返回同步条件
    CreateMessageWithText vsoPage, actorX(5), actorX(7), msgY, "19.返回同步条件", 1.4
    msgY = msgY - 0.4
    
    ' 20. 通知解析完成
    CreateMessageWithText vsoPage, actorX(7), actorX(6), msgY, "20.通知解析完成", 1.4
    msgY = msgY - 0.4
    
    ' 21. 平台错误检测
    CreateSelfMessageWithText vsoPage, actorX(4), msgY, "21.平台错误检测", 1.4
    msgY = msgY - 0.4
    
    ' 22. 查询平台状态
    CreateMessageWithText vsoPage, actorX(4), actorX(8), msgY, "22.查询平台状态", 1.4
    msgY = msgY - 0.4
    
    ' 23. 返回状态信息
    CreateMessageWithText vsoPage, actorX(8), actorX(4), msgY, "23.返回状态信息", 1.4
    msgY = msgY - 0.4
    
    ' 24. 发送平台错误信号
    CreateMessageWithText vsoPage, actorX(4), actorX(6), msgY, "24.发送平台错误信号", 1.6
    msgY = msgY - 0.4
    
    ' 25. 执行同步步骤
    CreateMessageWithText vsoPage, actorX(0), actorX(5), msgY, "25.执行同步步骤", 1.4
    msgY = msgY - 0.4
    
    ' 26. 装配同步条件
    CreateSelfMessageWithText vsoPage, actorX(5), msgY, "26.装配同步条件", 1.4
    msgY = msgY - 0.4
    
    ' 27. 检查偏差值
    CreateSelfMessageWithText vsoPage, actorX(5), msgY, "27.检查偏差值", 1.2
    msgY = msgY - 0.4
    
    ' 28. 返回同步结果
    CreateMessageWithText vsoPage, actorX(5), actorX(0), msgY, "28.返回同步结果", 1.4
    msgY = msgY - 0.4
    
    ' 29. 发布监控数据
    CreateMessageWithText vsoPage, actorX(1), actorX(0), msgY, "29.发布监控数据", 1.4
    msgY = msgY - 0.4
    
    ' 30. 停止监控
    CreateMessageWithText vsoPage, actorX(0), actorX(1), msgY, "30.停止监控", 1.2
    msgY = msgY - 0.4
    
    ' 31. 停止定时器
    CreateSelfMessageWithText vsoPage, actorX(1), msgY, "31.停止定时器", 1.2
    msgY = msgY - 0.4
    
    ' 32. 清理资源
    CreateSelfMessageWithText vsoPage, actorX(6), msgY, "32.清理资源", 1.2
    
    ' 添加激活框（白色填充）
    For i = 1 To 8
        Set vsoShape = vsoPage.DrawRectangle(actorX(i) - 0.1, 14, actorX(i) + 0.1, msgY - 0.2)
        vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
        vsoShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
    Next i
    
    ' 添加标题和说明
    Set vsoShape = vsoPage.DrawRectangle(1, 0.2, 14, 1.8)
    vsoShape.Text = "监视同步模块交互时序图说明：" & vbCrLf & _
                   "场景名：变量监控与文件同步协同处理" & vbCrLf & _
                   "场景描述：系统启动监视模块，进行变量监控、文件监视、平台状态检测和数据同步处理" & vbCrLf & _
                   "主要流程：模块初始化→监控启动→文件监视→数据同步→状态检测→资源清理" & vbCrLf & _
                   "关键特性：实时监控、自动同步、错误检测、资源管理"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(240,240,240)"
    vsoShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
    
    ' 设置页面视图
    vsoPage.Application.ActiveWindow.ViewFit = 1
    
    MsgBox "监视同步模块交互时序图创建完成！" & vbCrLf & vbCrLf & _
           "优化特点：" & vbCrLf & _
           "1. 完整展示监视同步模块协同工作流程" & vbCrLf & _
           "2. 涵盖变量监控、文件监视、平台状态检测" & vbCrLf & _
           "3. 体现数据同步和条件判断逻辑" & vbCrLf & _
           "4. 展示错误处理和资源管理机制" & vbCrLf & _
           "5. 参与者布局优化，避免消息重叠"
End Sub

' 创建带文本的消息函数
Sub CreateMessageWithText(vsoPage As Visio.Page, fromX As Double, toX As Double, y As Double, msgText As String, textWidth As Double)
    Dim msgShape As Visio.Shape
    Dim textShape As Visio.Shape

    ' 创建消息线
    Set msgShape = vsoPage.DrawLine(fromX, y, toX, y)
    msgShape.CellsU("EndArrow").FormulaU = "13"
    msgShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"

    ' 创建文本框（自适应宽度）
    Dim centerX As Double
    centerX = (fromX + toX) / 2
    Set textShape = vsoPage.DrawRectangle(centerX - textWidth / 2, y + 0.05, centerX + textWidth / 2, y + 0.25)
    textShape.Text = msgText
    textShape.CellsU("Char.Size").FormulaU = "8 pt"
    textShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    textShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
End Sub

' 创建自调用消息函数
Sub CreateSelfMessageWithText(vsoPage As Visio.Page, x As Double, y As Double, msgText As String, textWidth As Double)
    Dim msgShape1 As Visio.Shape
    Dim msgShape2 As Visio.Shape
    Dim msgShape3 As Visio.Shape
    Dim textShape As Visio.Shape

    ' 创建自调用消息的矩形路径
    Set msgShape1 = vsoPage.DrawLine(x, y, x + 1, y)
    msgShape1.CellsU("LineColor").FormulaU = "RGB(0,0,0)"

    Set msgShape2 = vsoPage.DrawLine(x + 1, y, x + 1, y - 0.2)
    msgShape2.CellsU("LineColor").FormulaU = "RGB(0,0,0)"

    Set msgShape3 = vsoPage.DrawLine(x + 1, y - 0.2, x, y - 0.2)
    msgShape3.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
    msgShape3.CellsU("EndArrow").FormulaU = "13"

    ' 创建文本框
    Set textShape = vsoPage.DrawRectangle(x + 1.1, y - 0.1, x + 1.1 + textWidth, y + 0.1)
    textShape.Text = msgText
    textShape.CellsU("Char.Size").FormulaU = "8 pt"
    textShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
    textShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
End Sub

' 创建数据同步专用时序图
Sub CreateDataSyncSequenceDiagram()
    Dim vsoPage As Visio.Page
    Dim vsoShape As Visio.Shape

    ' 创建新页面
    Set vsoPage = ActiveDocument.Pages.Add
    vsoPage.Name = "数据同步时序图"

    ' 设置页面大小
    vsoPage.PageSheet.CellsU("PageWidth").FormulaU = "20 in"
    vsoPage.PageSheet.CellsU("PageHeight").FormulaU = "14 in"

    ' 定义参与者位置和宽度
    Dim actorX(6) As Double
    Dim actorWidth(6) As Double

    actorX(0) = 2: actorWidth(0) = 1.6      ' 客户端界面
    actorX(1) = 5: actorWidth(1) = 2.0      ' SyncStyledDelegate
    actorX(2) = 8: actorWidth(2) = 2.2      ' VariableMonitorThread
    actorX(3) = 11.5: actorWidth(3) = 2.0   ' SynchronizeCondition
    actorX(4) = 14.5: actorWidth(4) = 1.8   ' 平台API
    actorX(5) = 17: actorWidth(5) = 1.8     ' 数据库
    actorX(6) = 19: actorWidth(6) = 1.6     ' 状态管理

    ' 创建参与者
    Set vsoShape = vsoPage.DrawRectangle(actorX(0) - actorWidth(0) / 2, 12.5, actorX(0) + actorWidth(0) / 2, 13.5)
    vsoShape.Text = "客户端界面"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(1) - actorWidth(1) / 2, 12.5, actorX(1) + actorWidth(1) / 2, 13.5)
    vsoShape.Text = "SyncStyledDelegate"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(2) - actorWidth(2) / 2, 12.5, actorX(2) + actorWidth(2) / 2, 13.5)
    vsoShape.Text = "VariableMonitorThread"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(3) - actorWidth(3) / 2, 12.5, actorX(3) + actorWidth(3) / 2, 13.5)
    vsoShape.Text = "SynchronizeCondition"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(4) - actorWidth(4) / 2, 12.5, actorX(4) + actorWidth(4) / 2, 13.5)
    vsoShape.Text = "平台API"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(5) - actorWidth(5) / 2, 12.5, actorX(5) + actorWidth(5) / 2, 13.5)
    vsoShape.Text = "数据库"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    Set vsoShape = vsoPage.DrawRectangle(actorX(6) - actorWidth(6) / 2, 12.5, actorX(6) + actorWidth(6) / 2, 13.5)
    vsoShape.Text = "状态管理"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"

    ' 创建生命线
    Dim i As Integer
    For i = 0 To 6
        Set vsoShape = vsoPage.DrawLine(actorX(i), 12.5, actorX(i), 2)
        vsoShape.CellsU("LinePattern").FormulaU = "2"
        vsoShape.CellsU("LineColor").FormulaU = "RGB(100,100,100)"
    Next i

    ' 创建数据同步消息序列
    Dim msgY As Double
    msgY = 12

    ' 1. 启动数据同步
    CreateMessageWithText vsoPage, actorX(0), actorX(2), msgY, "1.启动数据同步", 1.4
    msgY = msgY - 0.4

    ' 2. 获取对比信息
    CreateMessageWithText vsoPage, actorX(2), actorX(4), msgY, "2.获取对比信息", 1.4
    msgY = msgY - 0.4

    ' 3. 查询变量数据
    CreateMessageWithText vsoPage, actorX(4), actorX(5), msgY, "3.查询变量数据", 1.4
    msgY = msgY - 0.4

    ' 4. 返回数据结果
    CreateMessageWithText vsoPage, actorX(5), actorX(4), msgY, "4.返回数据结果", 1.4
    msgY = msgY - 0.4

    ' 5. 执行同步条件判断
    CreateMessageWithText vsoPage, actorX(2), actorX(3), msgY, "5.执行同步条件判断", 1.8
    msgY = msgY - 0.4

    ' 6. 检查偏差值
    CreateSelfMessageWithText vsoPage, actorX(3), msgY, "6.检查偏差值", 1.2
    msgY = msgY - 0.4

    ' 7. 返回同步结果
    CreateMessageWithText vsoPage, actorX(3), actorX(2), msgY, "7.返回同步结果", 1.4
    msgY = msgY - 0.4

    ' 8. 更新监控缓存
    CreateSelfMessageWithText vsoPage, actorX(2), msgY, "8.更新监控缓存", 1.4
    msgY = msgY - 0.4

    ' 9. 发布监控数据
    CreateMessageWithText vsoPage, actorX(2), actorX(0), msgY, "9.发布监控数据", 1.4
    msgY = msgY - 0.4

    ' 10. 设置高亮行
    CreateMessageWithText vsoPage, actorX(0), actorX(1), msgY, "10.设置高亮行", 1.2
    msgY = msgY - 0.4

    ' 11. 更新界面显示
    CreateSelfMessageWithText vsoPage, actorX(1), msgY, "11.更新界面显示", 1.4
    msgY = msgY - 0.4

    ' 12. 更新状态
    CreateMessageWithText vsoPage, actorX(0), actorX(6), msgY, "12.更新状态", 1.2

    ' 添加激活框
    For i = 1 To 6
        Set vsoShape = vsoPage.DrawRectangle(actorX(i) - 0.1, 12, actorX(i) + 0.1, msgY - 0.2)
        vsoShape.CellsU("FillForegnd").FormulaU = "RGB(255,255,255)"
        vsoShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"
    Next i

    ' 添加说明
    Set vsoShape = vsoPage.DrawRectangle(1, 0.2, 12, 1.5)
    vsoShape.Text = "数据同步时序图说明：" & vbCrLf & _
                   "场景名：变量数据同步与界面更新" & vbCrLf & _
                   "场景描述：监控线程获取变量数据，执行同步条件判断，更新界面高亮显示" & vbCrLf & _
                   "关键流程：数据获取→条件判断→结果同步→界面更新"
    vsoShape.CellsU("FillForegnd").FormulaU = "RGB(240,240,240)"
    vsoShape.CellsU("LineColor").FormulaU = "RGB(0,0,0)"

    ' 设置页面视图
    vsoPage.Application.ActiveWindow.ViewFit = 1

    MsgBox "数据同步时序图创建完成！"
End Sub
