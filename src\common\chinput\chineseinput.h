
#ifndef chineseinput_h_
#define chineseinput_h_

#include <QInputMethod>
#include <QtCore>
#include <QtWidgets>

/**
 * @brief WebAssembly 平台下的中文输入辅助类
 *        提供弹窗输入、与输入法交互等功能
 */
class WASMChineseInput : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    WASMChineseInput(QObject* parent = nullptr);

public:
    /**
     * @brief 获取用户输入（弹窗或输入法）
     * @param currentText 当前文本
     * @return 用户输入结果
     */
    QString getUserInput(const QString& currentText);
};

/**
 * @brief WASM 平台下的输入事件过滤器，自动弹出中文输入辅助
 *        支持 QLineEdit/QTextEdit/QPlainTextEdit 等控件
 */
class WASMChineseInputEventFilter : public QObject {
    Q_OBJECT
public:
    /**
     * @brief 构造函数，自动注册全局事件过滤器（仅WASM平台）
     * @param parent 父对象
     */
    WASMChineseInputEventFilter(QObject* parent = nullptr)
    {
#ifdef Q_OS_WASM
        qApp->installEventFilter(this);
#endif
    }

    /**
     * @brief 判断是否需要弹出输入辅助控件
     * @param object 目标对象
     * @param event 事件对象
     * @return 是否需要弹出
     */
    bool isNeedShowInputWidget(QObject* object, QEvent* event)
    {
        if (!object || !event)
            return false;

        // 屏蔽日期、时间、日历、数字等控件
        if (qobject_cast<QDateEdit*>(object) != nullptr
            || qobject_cast<QTimeEdit*>(object) != nullptr
            || qobject_cast<QDateTimeEdit*>(object) != nullptr
            || qobject_cast<QCalendarWidget*>(object) != nullptr
            || qobject_cast<QSpinBox*>(object) != nullptr
            || qobject_cast<QDoubleSpinBox*>(object) != nullptr) {
            return false;
        }

        // 仅对可编辑的 QLineEdit 鼠标点击弹出
        if (qobject_cast<QLineEdit*>(object) != nullptr
            && !qobject_cast<QLineEdit*>(object)->isReadOnly()
            && qobject_cast<QLineEdit*>(object)->isEnabled()
            && qobject_cast<QLineEdit*>(object)->echoMode() == QLineEdit::Normal
            && event->type() == QEvent::MouseButtonPress) {
            return true;
        }
        // 仅对可编辑的 QTextEdit/PlainTextEdit 手写板点击弹出
        if (qobject_cast<QTextEdit*>(object) != nullptr
            && !qobject_cast<QTextEdit*>(object)->isReadOnly()
            && qobject_cast<QTextEdit*>(object)->isEnabled()
            && event->type() == QEvent::TabletPress) {
            return true;
        }
        if (qobject_cast<QPlainTextEdit*>(object) != nullptr
            && !qobject_cast<QPlainTextEdit*>(object)->isReadOnly()
            && qobject_cast<QPlainTextEdit*>(object)->isEnabled()
            && event->type() == QEvent::TabletPress) {
            return true;
        }
        // QComboBox* comboBox = qobject_cast<QComboBox*>(object);
        // if (comboBox != nullptr && comboBox->isEditable()) {
        //     return true;
        // }

        return false;
    }

    /**
     * @brief 设置控件文本内容
     * @param object 目标对象
     * @param text 文本内容
     * @return 是否设置成功
     */
    bool setText(QObject* object, const QString& text)
    {
        if (qobject_cast<QLineEdit*>(object) != nullptr) {
            qobject_cast<QLineEdit*>(object)->setText(text);
            return true;
        }
        if (qobject_cast<QTextEdit*>(object) != nullptr) {
            qobject_cast<QTextEdit*>(object)->setText(text);
            return true;
        }
        if (qobject_cast<QPlainTextEdit*>(object) != nullptr) {
            qobject_cast<QPlainTextEdit*>(object)->setPlainText(text);
            return true;
        }
        // QComboBox* comboBox = qobject_cast<QComboBox*>(object);
        // if (comboBox != nullptr && comboBox->isEditable()) {
        //     qobject_cast<QComboBox*>(object)->setCurrentText(text);
        //     return true;
        // }
        return false;
    }

    /**
     * @brief 获取控件文本内容
     * @param object 目标对象
     * @return 文本内容
     */
    QString text(QObject* object)
    {
        if (qobject_cast<QLineEdit*>(object) != nullptr) {
            return qobject_cast<QLineEdit*>(object)->text();
        }
        if (qobject_cast<QTextEdit*>(object) != nullptr) {
            return qobject_cast<QTextEdit*>(object)->toPlainText();
        }
        if (qobject_cast<QPlainTextEdit*>(object) != nullptr) {
            return qobject_cast<QPlainTextEdit*>(object)->toPlainText();
        }
        // QComboBox* comboBox = qobject_cast<QComboBox*>(object);
        // if (comboBox != nullptr && comboBox->isEditable()) {
        //     return qobject_cast<QComboBox*>(object)->currentText();
        // }
        return "";
    }

protected:
    /**
     * @brief 事件过滤器，WASM下自动弹出输入辅助
     * @param watched 被监控对象
     * @param event 事件对象
     * @return 是否拦截
     */
    bool eventFilter(QObject* watched, QEvent* event)
    {
#ifdef Q_OS_WASM
        if (isNeedShowInputWidget(watched, event)) {
            const QString _text = WASMChineseInput().getUserInput(text(watched));
            setText(watched, _text.trimmed());
            return true;
        }
#endif
        return QObject::eventFilter(watched, event);
    }
};

#endif
