<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProjectsPage2</class>
 <widget class="QWidget" name="ProjectsPage2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1119</width>
    <height>693</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <widget class="QWidget" name="widgetLeft" native="true">
     <property name="maximumSize">
      <size>
       <width>200</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="QToolButton" name="toolButtonProjectList">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <property name="text">
         <string>  Project List</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="toolButtonStyle">
         <enum>Qt::ToolButtonTextBesideIcon</enum>
        </property>
        <property name="styleclass" stdset="0">
         <string notr="true">projectlist</string>
        </property>
        <attribute name="buttonGroup">
         <string notr="true">buttonGroup</string>
        </attribute>
       </widget>
      </item>
      <item>
       <widget class="QToolButton" name="toolButtonRecentlyOpen">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <property name="text">
         <string>  Recently Opened</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <property name="toolButtonStyle">
         <enum>Qt::ToolButtonTextBesideIcon</enum>
        </property>
        <property name="styleclass" stdset="0">
         <string notr="true">recentopen</string>
        </property>
        <attribute name="buttonGroup">
         <string notr="true">buttonGroup</string>
        </attribute>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>Project Opened:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="labelCurrentProjectName">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>-</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonLogout">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="text">
         <string>Log Out</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonReturn">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="text">
         <string>Return</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="pushButtonRefresh">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="text">
         <string>Refresh</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widgetCenter" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="QLabel" name="labelCurrentListTitle">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>26</height>
         </size>
        </property>
        <property name="text">
         <string notr="true">-</string>
        </property>
        <property name="styleclass" stdset="0">
         <string notr="true">project-list-title</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QListView" name="listView"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widgetRight" native="true">
     <property name="minimumSize">
      <size>
       <width>340</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>400</width>
       <height>16777215</height>
      </size>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
