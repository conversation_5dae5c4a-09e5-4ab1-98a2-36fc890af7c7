#include "projectwidget.h"
#include "ui_projectwidget.h"

ProjectWidget::ProjectWidget(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::ProjectWidget)
{
    ui->setupUi(this);
    // ui->dateTimeEdit->setStyleSheet("border: 0px;");
    this->setStyleSheet("background-color: transparent;");
}

ProjectWidget::~ProjectWidget()
{
    delete ui;
}

void ProjectWidget::setProjectName(const QString& name)
{
    ui->label->setText(name);
}

void ProjectWidget::setCreateTime(const QString& time)
{
    QString _time = time;
    ui->labelDateTime->setText(_time.replace("T", " "));
}

void ProjectWidget::setChecked(bool checked)
{
    setHovered(checked);
    m_isChecked = checked;
    if (checked) {
        ui->frameBlock->setStyleSheet("background-color: #AFC6FE;");
    } else {
        ui->frameBlock->setStyleSheet("background-color: transparent;");
    }
}

void ProjectWidget::setHovered(bool hover)
{
    if (!m_isChecked) {
        if (hover) {
            setStyleSheet("background-color: #f1eeee;");
        } else {
            setStyleSheet("background-color: transparent;");
        }
    }
}

bool ProjectWidget::event(QEvent* event)
{
    if (event->type() == QEvent::LanguageChange) {
        ui->retranslateUi(this);
    }

    return QWidget::event(event);
}
