#ifndef PROJECTINFOWIDGT_H
#define PROJECTINFOWIDGT_H

#include "qabstractitemmodel.h"
#include "uiglobalstate.h"
// #include "global_structs.h"
// #include "global_structs.pb.h"
#include <QWidget>

namespace Ui {
class ProjectInfoWidgt;
}

class UIGlobalState;
class ProjectInfoWidgt : public QWidget {
    Q_OBJECT

public:
    explicit ProjectInfoWidgt(UIGlobalState* gl, QWidget* parent = nullptr);
    ~ProjectInfoWidgt();
    /**
     * @brief setProjectName 设置工程名
     * @param name 工程名
     */
    void setProjectName(const QString& name);
    /**
     * @brief setCreateTime 设置创建时间
     * @param time 创建时间
     */
    void setCreateTime(const QString& time);
    /**
     * @brief setProjectDesc 设置工程描述
     * @param desc 工程描述
     */
    void setProjectDesc(const QString& desc);
    /**
     * @brief setProjectPath 设置工程路径
     * @param path 工程路径
     */
    void setProjectPath(const QString& path);
    /**
     * @brief setOpenedTime 设置打开时间
     * @param time 打开时间
     */
    void setOpenedTime(const QString& time);
    /**
     * @brief setStationNum 设置站点数量
     * @param num 站点数量
     */
    void setStationNum(int num);
    /**
     * @brief setProjectID 设置工程id
     * @param projectid 工程id
     */
    void setProjectID(int projectid);
    /**
     * @brief showInfoWidget 显示信息
     */
    void showInfoWidget();
    /**
     * @brief hideInfoWidget 隐藏信息
     */
    void hideInfoWidget();

    void setIndex(const QModelIndex& index);
public slots:
    void openProject();

protected:
    bool event(QEvent* event);

private:
    Ui::ProjectInfoWidgt* ui;
    QModelIndex m_index;
    UIGlobalState* global = nullptr;
};

#endif // PROJECTINFOWIDGT_H
