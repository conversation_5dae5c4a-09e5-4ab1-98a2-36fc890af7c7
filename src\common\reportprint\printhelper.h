#ifndef PRINTHELPER_H
#define PRINTHELPER_H

#include <qnamespace.h>
class QColor;
class QPixmap;
class QPrinter;
class QPoint;
class QFont;
class QMargins;
#ifdef SUPPORT_PDF
class QPdfDocument;
#endif
class QAbstractItemModel;

/**
 * @brief 打印辅助工具类
 *
 * 提供表格/图片/模型等多种内容的打印参数设置与打印实现，支持PDF、QPrinter等多平台兼容。
 * 适用于Qt桌面应用的报表、表格、图片批量打印。
 *
 * 主要功能：
 * - 设置标题、表头颜色、边距、字体、对齐方式等
 * - 支持打印QPixmap、QAbstractItemModel、PDF文档
 * - 支持多种打印参数的静态全局设置
 * - 释放打印相关资源
 */
class PrintHelper
{
public:
    /**
     * @brief 构造函数
     */
    PrintHelper();

    /**
     * @brief 设置打印标题
     * @param title 标题字符串
     */
    static void setTile(const QString &title);

    /**
     * @brief 设置表头颜色
     * @param color 颜色对象
     */
    static void setHeaderColor(const QColor &color);

    /**
     * @brief 设置页面边距
     * @param margins 边距对象
     */
    static void setMargin(const QMargins &margins);
    /**
     * @brief 设置文本偏移量
     * @param offset 偏移点
     */
    static void setTextOffset(const QPoint &offset);
    /**
     * @brief 设置字体
     * @param font 字体对象
     */
    static void setFont(const QFont &font);
    /**
     * @brief 设置单元格线宽
     * @param width 线宽
     */
    static void setCellLineWidth(int width);
    /**
     * @brief 设置列宽权重
     * @param columnWidths 列宽权重列表
     */
    static void setcolWidthsWeights(const QList<int> &columnWidths);
    /**
     * @brief 设置对齐方式
     * @param alignment 对齐方式
     */
    static void setAlignment(Qt::Alignment &alignment);
private:
    friend class PrintPreviewDialog;
    /**
     * @brief 打印图片
     * @param printer 打印机对象
     * @param pixmap 图片对象
     */
    static void printPixmap(QPrinter *, const QPixmap &);
    /**
     * @brief 打印模型（如表格）
     * @param printer 打印机对象
     * @param model 数据模型
     */
    static void PrintModel(QPrinter *, QAbstractItemModel *);
#ifdef SUPPORT_PDF
    /**
     * @brief 打印PDF文档
     * @param printer 打印机对象
     * @param pdf PDF文档对象
     */
    static void PrintPdf(QPrinter *, QPdfDocument *);
#endif
    /**
     * @brief 释放打印相关资源
     */
    static void releaseData();
};

#endif // PRINTHELPER_H
